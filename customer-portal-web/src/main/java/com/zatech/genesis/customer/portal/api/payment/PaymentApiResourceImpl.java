package com.zatech.genesis.customer.portal.api.payment;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.gaia.resource.components.enums.policymanagement.NBConfigurationTypeEnum;
import com.zatech.genesis.customer.portal.api.errorcode.PaymentCheckErrorCode;
import com.zatech.genesis.customer.portal.api.payment.builder.PaymentConverter;
import com.zatech.genesis.customer.portal.api.payment.enums.PayStatusEnum;
import com.zatech.genesis.customer.portal.api.payment.request.PaymentRequest;
import com.zatech.genesis.customer.portal.api.payment.response.CheckPaymentInfoResponse;
import com.zatech.genesis.customer.portal.api.payment.response.PaymentResponse;
import com.zatech.genesis.customer.portal.api.payment.response.QueryPaymentInfoResponse;
import com.zatech.genesis.customer.portal.integration.outer.openapi.payment.request.PaymentOrderRequest;
import com.zatech.genesis.customer.portal.integration.outer.openapi.payment.response.PayOrderResp;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.Policy;
import com.zatech.genesis.customer.portal.integration.outer.policy.IOuterPolicyService;
import com.zatech.genesis.customer.portal.service.openapi.PaymentOpenApiService;
import com.zatech.genesis.customer.portal.service.openapi.PolicyOpenApiService;
import com.zatech.genesis.customer.portal.web.WebErrorCodes;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.policy.api.response.IssuanceRuleResponse;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@Slf4j
public class PaymentApiResourceImpl implements PaymentApi {

    private final PolicyOpenApiService policyOpenApiService;

    private final PaymentOpenApiService paymentOpenApiService;

    private final IOuterPolicyService policyService;

    private final PaymentConverter paymentConverter;

    // 用于记录已经确认过的投保单，避免重复确认
    private final ConcurrentHashMap<String, Boolean> confirmedProposals = new ConcurrentHashMap<>();

    @Override
    public CheckPaymentInfoResponse check(String issuanceNo) {

        Policy policy = policyOpenApiService.queryIssuanceByIssuanceNo(issuanceNo);

        validateBasicInfo(policy);

        validatePremiumChange(policy);

        return CheckPaymentInfoResponse.builder().success(true).build();
    }

    @Override
    public QueryPaymentInfoResponse queryPayOrder(String issuanceNo, String payOrderNo) {

        PayOrderResp payOrderResp = paymentOpenApiService.queryPaymentOrder(payOrderNo);
        if (payOrderResp == null) {
            throw CommonException.byError(PaymentCheckErrorCode.PAYMENT_PAYER_NOT_FOUND);
        }

        if (isPaymentSuccessful(payOrderResp)) {
            doConfirmApplication(issuanceNo);
        }

        return convertQueryPaymentInfoResponse(payOrderResp);
    }

    @Override
    public PaymentResponse pay(String issuanceNo, PaymentRequest request) {

        Policy policy = policyOpenApiService.queryIssuanceByIssuanceNo(issuanceNo);

        if (paymentOpenApiService.existPayedOrder(TransTypeEnum.EFECTIVE_POLICY, issuanceNo)) {
            throw CommonException.byError(PaymentCheckErrorCode.PAYMENT_ORDER_STATUS_PAYED);
        }

        if (paymentOpenApiService.existPayingOrder(TransTypeEnum.EFECTIVE_POLICY, issuanceNo)) {
            //针对Stripe支付方式可以查到对应链接再次支付，后续如果出现其他支付需要针对对应支付方式处理使用不同handle处理，例如取消后在进行支付
            return paymentOpenApiService.queryPaymentOrder(TransTypeEnum.EFECTIVE_POLICY, issuanceNo).stream().filter(x -> x.getPayStatus() == com.zatech.gaia.resource.components.enums.paymentgateway.PayStatusEnum.PAYING).findFirst().map(this::convertPaymentResponse).orElse(null);
        }

        PaymentOrderRequest paymentOrderRequest = paymentConverter.convert(request, policy);

        PaymentResponse paymentResponse = new PaymentResponse();
        try {
            PayOrderResp paymentOrder = paymentOpenApiService.createPaymentOrder(paymentOrderRequest);
            paymentResponse = convertPaymentResponse(paymentOrder);
        } catch (Exception e) {
            log.warn("CreatePaymentProcessor.process exception ", e);
            paymentResponse.setPayDate(new Date());
            paymentResponse.setStatus(PayStatusEnum.FAILED);
        }
        return paymentResponse;
    }

    private void validatePremiumChange(Policy policy) {

        Policy oldVersionPolicy = policyOpenApiService.queryIssuanceByIssuanceNoAndVersion(policy.getProposalNo(), "1");
        if (!oldVersionPolicy.getActualPremium().equals(policy.getActualPremium())) {
            throw CommonException.byErrorAndParams(PaymentCheckErrorCode.PAYMENT_PREMIUM_CHANGED, oldVersionPolicy.getActualPremium(), policy.getActualPremium());
        }


    }

    /**
     * 是否有人核记录
     *
     * @return
     */
    protected boolean hasManualRecord(IssuanceResponse response) {
        List<IssuanceRuleResponse> issuanceRuleResponses = policyService.queryIssuanceRules(response.getIssuanceNo(), null, null);
        log.info("Rule decision : {}", StaticJsonParser.toJsonString(issuanceRuleResponses));
        return Optional.ofNullable(issuanceRuleResponses)
                .orElse(Collections.emptyList())
                .stream()
                .anyMatch(d -> NBConfigurationTypeEnum.PROPOSAL_UNDERWRITING == d.getType() && MANUAL_STATUS.contains(d.getStatus()));
    }

    private PaymentResponse convertPaymentResponse(PayOrderResp payOrderResp) {

        PaymentResponse paymentResponse = new PaymentResponse();
        paymentResponse.setPayDate(Optional.ofNullable(payOrderResp).map(PayOrderResp::getPayDate).orElse(new Date()));
        paymentResponse.setStatus(payOrderResp == null ? PayStatusEnum.FAILED : paymentConverter.convert(payOrderResp.getPayStatus()));
        Optional.ofNullable(payOrderResp).map(PayOrderResp::getPayOrderNo).ifPresent(paymentResponse::setThirdPartyOrderNo);
        Optional.ofNullable(payOrderResp).map(PayOrderResp::getRedirectUrl).ifPresent(paymentResponse::setRedirectUrl);
        Optional.ofNullable(payOrderResp).map(PayOrderResp::getFormParams).ifPresent(paymentResponse::addParams);
        return paymentResponse;
    }

    private void doConfirmApplication(String issuanceNo) {

        // 双重检查锁定模式确保幂等性
        if (confirmedProposals.containsKey(issuanceNo)) {
            log.info("Proposal {} confirmation already in progress or completed", issuanceNo);
            return;
        }

        synchronized (this) {
            if (confirmedProposals.containsKey(issuanceNo)) {
                log.info("Proposal {} confirmation already in progress or completed", issuanceNo);
                return;
            }

            Policy policy = policyOpenApiService.queryIssuanceByIssuanceNo(issuanceNo);

            // 检查投保单状态，如果已经不是等待出单状态，说明已经确认过了
            if (policy.getProposalStatus() != IssuanceStatusEnum.WAITING_FOR_ISSUANCE) {
                log.info("Proposal {} has already been confirmed, current status: {}", issuanceNo, policy.getProposalStatus());
                confirmedProposals.put(issuanceNo, true);
                return;
            }

            // 标记为正在处理
            confirmedProposals.put(issuanceNo, true);

            try {
                policyOpenApiService.confirmApplication(policy);
                log.info("Proposal {} confirmation completed successfully", issuanceNo);
            } catch (Exception e) {
                // 如果确认失败，移除标记以允许重试
                confirmedProposals.remove(issuanceNo);
                log.error("Failed to confirm proposal {}, removed from confirmed list", issuanceNo, e);
                throw e;
            }
        }
    }

    private boolean isPaymentSuccessful(PayOrderResp payOrderResp) {

        return payOrderResp.getPayStatus() == com.zatech.gaia.resource.components.enums.paymentgateway.PayStatusEnum.SUCCESS;
    }

    private QueryPaymentInfoResponse convertQueryPaymentInfoResponse(PayOrderResp payOrderResp) {

        if (payOrderResp != null) {
            QueryPaymentInfoResponse response = new QueryPaymentInfoResponse();
            response.setStatus(paymentConverter.convert(payOrderResp.getPayStatus()));
            response.setThirdPartyOrderNo(payOrderResp.getPayOrderNo());
            response.setAmount(payOrderResp.getAmount());
            response.setPayDate(payOrderResp.getPayDate());
            response.setCurrency(payOrderResp.getCurrency().name());
            return response;
        }
        return null;
    }

    private void validateBasicInfo(Policy policy) {

        if (policy == null) {
            throw CommonException.byError(WebErrorCodes.POLICY_NOT_FOUND);
        }
        if (policy.getProposalStatus() != IssuanceStatusEnum.WAITING_FOR_ISSUANCE) {
            throw CommonException.byError(WebErrorCodes.POLICY_STATUS_ERROR);
        }
    }

}
