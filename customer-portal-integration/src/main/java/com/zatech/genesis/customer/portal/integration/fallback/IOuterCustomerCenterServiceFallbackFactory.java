package com.zatech.genesis.customer.portal.integration.fallback;

import com.zatech.genesis.customer.portal.api.user.request.QueryUserIdentifyRequest;
import com.zatech.genesis.customer.portal.api.user.request.ValidateVerificationCodeRequest;
import com.zatech.genesis.customer.portal.api.user.response.QueryUserIdentifyResponse;
import com.zatech.genesis.customer.portal.api.user.response.ValidateVerificationCodeResponse;
import com.zatech.genesis.customer.portal.common.exception.CustomerCenterErrorCodes;
import com.zatech.genesis.customer.portal.integration.outer.cc.IOuterCustomerCenterService;
import com.zatech.genesis.customer.portal.integration.outer.cc.request.CcChangeMobileRequest;
import com.zatech.genesis.customer.portal.integration.outer.cc.request.CcChangePasswordRequest;
import com.zatech.genesis.customer.portal.integration.outer.cc.request.CcChangeEmailRequest;
import com.zatech.genesis.customer.portal.integration.outer.cc.request.CcChannelCustomerRequest;
import com.zatech.genesis.customer.portal.integration.outer.cc.request.CcLoginPwdRequest;
import com.zatech.genesis.customer.portal.integration.outer.cc.request.CcRegisterPwdRequest;
import com.zatech.genesis.customer.portal.integration.outer.cc.request.CcResetPasswordRequest;
import com.zatech.genesis.customer.portal.integration.outer.cc.request.CcSendVerificationCodeRequest;
import com.zatech.genesis.customer.portal.integration.outer.cc.response.CcInfoResponse;
import com.zatech.genesis.customer.portal.integration.outer.cc.response.CcLoginResponse;
import com.zatech.genesis.customer.portal.integration.outer.cc.response.CcQueryChannelCustomerResponse;
import com.zatech.genesis.customer.portal.integration.outer.cc.response.CcRegisterResponse;
import com.zatech.genesis.customer.portal.integration.outer.cc.response.CcSendVerificationCodeResponse;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;
import com.zatech.genesis.portal.toolbox.exception.mapping.ISourceErrorMapping;
import com.zatech.octopus.core.util.JacksonUtil;

import feign.Response;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/1
 */
@Slf4j
@Component
public class IOuterCustomerCenterServiceFallbackFactory extends AbstractFallbackFactory<IOuterCustomerCenterService> implements ISourceErrorMapping {

    private static final String ERROR = "\n <<[Method]: {}\n[request info]: {}\n[cause]: {}";

    @Override
    public IOuterCustomerCenterService create(Throwable cause) {
        return new IOuterCustomerCenterService() {
            @Override
            public Response generateCaptcha(String sessionId) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.generateCaptcha: sessionId:{}",
                        sessionId, cause.getMessage());
                throwCustomerCenterException(cause);
                return null;
            }

            @Override
            public CcSendVerificationCodeResponse sendVerificationCode(String sessionId, CcSendVerificationCodeRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.sendVerificationCode: sessionId:{}, request:{}",
                        sessionId, JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
                return null;
            }

            @Override
            public CcRegisterResponse registerMobilePwd(CcRegisterPwdRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.registerMobilePwd: request:{}",
                        JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
                return null;
            }

            @Override
            public CcRegisterResponse registerEmailPwd(CcRegisterPwdRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.registerEmailPwd: request:{}",
                        JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
                return null;
            }

            @Override
            public CcLoginResponse loginMobilePwd(CcLoginPwdRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.loginMobilePwd: request:{}",
                        JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
                return null;
            }

            @Override
            public CcLoginResponse loginEmailPwd(CcLoginPwdRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.loginEmailPwd: request:{}",
                        JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
                return null;
            }

            @Override
            public CcInfoResponse getChannelCustomerInfo(Long channelCustomerId) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.getChannelCustomerInfo: channelCustomerId:{}",
                        channelCustomerId, cause.getMessage());
                throwCustomerCenterException(cause);
                return null;
            }

            @Override
            public void updateChannelCustomerInfo(Long channelCustomerId, CcChannelCustomerRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.updateChannelCustomerInfo: channelCustomerId:{}, request:{}",
                        channelCustomerId, JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
            }

            @Override
            public void resetChannelCustomerPasswordByEmail(CcResetPasswordRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.resetChannelCustomerPasswordByEmail: request:{}",
                        JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
            }

            @Override
            public void resetChannelCustomerPasswordByMobile(CcResetPasswordRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.resetChannelCustomerPasswordByMobile: request:{}",
                        JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
            }

            @Override
            public void changeChannelCustomerPassword(Long channelCustomerId, CcChangePasswordRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.changeChannelCustomerPassword: request:{}",
                    JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
            }

            @Override
            public boolean captchaSwitch() {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.captchaSwitch", cause.getMessage());
                throwCustomerCenterException(cause);
                return false;
            }

            @Override
            public ValidateVerificationCodeResponse validate(ValidateVerificationCodeRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.validate: request:{}",
                        JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
                return new ValidateVerificationCodeResponse();
            }

            @Override
            public List<QueryUserIdentifyResponse> queryIdentifyUser(QueryUserIdentifyRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.queryIdentifyUser: request:{}",
                        JacksonUtil.toJSONString(request), cause.getMessage());
                throwCustomerCenterException(cause);
                return null;
            }

            @Override
            public List<QueryUserIdentifyResponse> queryAnotherUserAccount(Long channelCustomerId) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.queryAnotherUserAccount: request:{}",
                        channelCustomerId, cause.getMessage());
                throwCustomerCenterException(cause);
                return null;
            }

            @Override
            public void updateChannelCustomerInfoCascadeAccount(Long channelCustomerId, CcChannelCustomerRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.updateChannelCustomerInfoCascadeAccount: request:{}",
                        channelCustomerId, cause.getMessage());
                throwCustomerCenterException(cause);
            }

            @Override
            public Long getPersonIdByChannelCustomerId(Long channelCustomerId) {
                throwCustomerCenterException(cause);
                return null;
            }

            @Override
            public CcQueryChannelCustomerResponse queryChannelCustomer(Long channelCustomerId) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.queryChannelCustomer: request:{}, {}",
                    channelCustomerId, cause.getMessage(), cause);
                return null;
            }

            @Override
            public boolean checkUserUniqueElements(Long channelCustomerId) {
                return false;
            }

            @Override
            public void changeChannelCustomerEmail(Long channelCustomerId, CcChangeEmailRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.changeChannelCustomerEmail: request:{}",
                    channelCustomerId, cause.getMessage());
                throwCustomerCenterException(cause);
            }

            @Override
            public void changeChannelCustomerMobile(Long channelCustomerId, CcChangeMobileRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCustomerCenterServiceFallbackFactory.changeChannelCustomerEmail: request:{}",
                    channelCustomerId, cause.getMessage());
                throwCustomerCenterException(cause);
            }

            private void throwCustomerCenterException(Throwable cause) {
                throw outerServiceException(cause);
            }
        };
    }
    @Override
    public String outerServiceKey() {
        return "cc";
    }

    @Override
    public Class<? extends IErrorCode>[] mappingErrorCodes() {
        return new Class[]{CustomerCenterErrorCodes.class};
    }
}
