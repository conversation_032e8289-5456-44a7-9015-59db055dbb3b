package com.zatech.genesis.customer.portal.integration.fallback;

import com.zatech.genesis.customer.portal.integration.outer.salesjourney.IOuterSalesJourneyService;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;

import org.springframework.stereotype.Component;

@Component
public class IOuterSalesJourneyServiceFallbackFactory extends AbstractFallbackFactory<IOuterSalesJourneyService> {

    @Override
    protected String outerServiceKey() {
        return "sj";
    }

    @Override
    public IOuterSalesJourneyService create(Throwable cause) {
        throw outerServiceException(cause);
    }

}
