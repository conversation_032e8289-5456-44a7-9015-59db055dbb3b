/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.integration.fallback;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.doc.DocTypeEnum;
import com.zatech.genesis.customer.portal.integration.outer.openapi.IOuterOpenApiService;
import com.zatech.genesis.customer.portal.integration.outer.openapi.OpenApiErrorCodes;
import com.zatech.genesis.customer.portal.integration.outer.openapi.PageInfo;
import com.zatech.genesis.customer.portal.integration.outer.openapi.claim.ClaimQueryCaseByCaseNoResponse;
import com.zatech.genesis.customer.portal.integration.outer.openapi.claim.CreateClaimCaseRequest;
import com.zatech.genesis.customer.portal.integration.outer.openapi.claim.PolicyClaimApplicationApiResponse;
import com.zatech.genesis.customer.portal.integration.outer.openapi.claim.PolicyClaimCaseApiResponse;
import com.zatech.genesis.customer.portal.integration.outer.openapi.market.GoodsRelatingOpenApiResponse;
import com.zatech.genesis.customer.portal.integration.outer.openapi.market.element.PackageApplicationElementsOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.market.element.QueryPackageApplicationElementsInput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.metadata.QueryMetadataDictOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.payment.request.PaymentOrderRequest;
import com.zatech.genesis.customer.portal.integration.outer.openapi.payment.request.SearchPayOrderRequest;
import com.zatech.genesis.customer.portal.integration.outer.openapi.payment.response.PayOrderResp;
import com.zatech.genesis.customer.portal.integration.outer.openapi.payment.response.SearchPaymentResponse;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pendingcase.deletefile.output.PendingCaseDeleteFileOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pendingcase.received.input.ReceivedPendingCaseInput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pendingcase.received.output.ReceivedPendingCaseOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pendingcase.response.PendingCaseDetailResponse;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pendingcase.savefile.input.PendingCaseSaveFileInput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pendingcase.savefile.output.PendingCaseSaveFileListOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.Policy;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.confirm.ConfirmApplicationInput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.confirm.ConfirmApplicationOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.document.request.DocumentDownloadRequest;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.document.response.DocumentDownloadResponse;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.document.response.PolicyDocumentResponse;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.output.PolicyOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.output.UpdateQuotationOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.output.WithdrawApplicationOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.calculate.PosCalculationInput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.calculate.PosCalculationOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.cancel.input.CancelInput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.cancel.output.CancelOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.change.input.ChangeInfoInput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.change.output.ChangeInfoOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.freelook.input.FreeLookInput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.freelook.output.FreeLookOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.model.PosDetailOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.query.PosQueryListResultOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.underwriting.PosUnderwritingInput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.underwriting.PosUnderwritingOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.product.output.ProductOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.product.output.ProductResponse;
import com.zatech.genesis.customer.portal.integration.outer.product.request.QueryLiabilityListRequest;
import com.zatech.genesis.customer.portal.integration.outer.product.response.QueryLiabilityListResponse;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;
import com.zatech.genesis.portal.toolbox.exception.mapping.ISourceErrorMapping;
import com.zatech.genesis.portal.toolbox.exception.mapping.ISourceErrorMappingDefault;

import java.util.List;

import org.springframework.stereotype.Component;

@Component
public class IOuterOpenApiServiceFallbackFactory extends AbstractFallbackFactory<IOuterOpenApiService> implements ISourceErrorMapping,
    ISourceErrorMappingDefault {

    @Override
    public IOuterOpenApiService create(Throwable cause) {

        return new IOuterOpenApiService() {

            @Override
            public Policy queryPolicyDetail(String policyNo) {

                throw outerServiceException(cause);
            }

            @Override
            public GoodsRelatingOpenApiResponse queryGoodsRelatingList(String goodsCode) {

                throw outerServiceException(cause);
            }

            @Override
            public ProductOutput queryProductDetail(String productCode, String bizDate) {

                throw outerServiceException(cause);
            }

            @Override
            public ProductResponse queryProductList(List<String> productCodes, String bizDate) {

                throw outerServiceException(cause);
            }

            @Override
            public Policy queryApplication(String proposalNo, YesNoEnum temporary) {

                throw outerServiceException(cause);
            }

            @Override
            public Policy queryApplicationByVersion(String proposalNo, YesNoEnum temporary, String version) {

                throw outerServiceException(cause);
            }

            @Override
            public QueryMetadataDictOutput queryDict(String dictKeys) {

                throw outerServiceException(cause);
            }

            @Override
            public PolicyOutput create(Policy policy) {

                throw outerServiceException(cause);
            }

            @Override
            public ConfirmApplicationOutput applicationConfirm(String proposalNo, ConfirmApplicationInput input) {

                throw outerServiceException(cause);
            }

            @Override
            public UpdateQuotationOutput change(String applicationNo, Policy policy) {

                throw outerServiceException(cause);
            }

            @Override
            public PackageApplicationElementsOutput queryPackageApplicationElements(String packageCode,
                                                                                    QueryPackageApplicationElementsInput elementsInput) {

                throw outerServiceException(cause);
            }

            @Override
            public PolicyDocumentResponse printPolicy(String policyNo, DocTypeEnum docType) {

                throw outerServiceException(cause);
            }

            @Override
            public DocumentDownloadResponse documentDownloadBatch(DocumentDownloadRequest documentDownloadRequest) {

                throw outerServiceException(cause);
            }

            @Override
            public PosCalculationOutput calculation(String policyNo, PosCalculationInput input) {

                throw outerServiceException(cause);
            }

            @Override
            public PosUnderwritingOutput underwriting(String policyNo, PosUnderwritingInput input) {

                throw outerServiceException(cause);
            }

            @Override
            public CancelOutput policyCancel(String policyNo, CancelInput input) {

                throw outerServiceException(cause);
            }

            @Override
            public WithdrawApplicationOutput withdrawApplication(String proposalNo, YesNoEnum temporary) {
                throw outerServiceException(cause);
            }

            @Override
            public FreeLookOutput freeLook(String policyNo, FreeLookInput input) {

                throw outerServiceException(cause);
            }

            @Override
            public PageInfo<PolicyClaimCaseApiResponse> queryClaimList(String policyNo, Integer pageSize, Integer pageIndex) {

                throw outerServiceException(cause);
            }

            @Override
            public PageInfo<PosQueryListResultOutput> queryPosList(String policyNo, Integer pageSize, Integer pageIndex) {

                throw outerServiceException(cause);
            }

            @Override
            public ChangeInfoOutput change(String policyNo, ChangeInfoInput input) {

                throw outerServiceException(cause);
            }

            @Override
            public PolicyClaimCaseApiResponse createClaim(String policyNo, CreateClaimCaseRequest input) {
                throw outerServiceException(cause);
            }

            @Override
            public ClaimQueryCaseByCaseNoResponse queryClaimByCaseNo(String caseNo) {
                throw outerServiceException(cause);
            }

            @Override
            public PayOrderResp pay(PaymentOrderRequest paymentOrderRequest) {
                throw outerServiceException(cause);
            }

            @Override
            public PayOrderResp queryPayOrder(String payOrderNo) {
                throw outerServiceException(cause);
            }

            @Override
            public PosDetailOutput queryPosDetail(String policyNo, String caseNo) {
                throw outerServiceException(cause);
            }

            @Override
            public PendingCaseSaveFileListOutput savePendingCaseFiles(String pendingCaseNo, PendingCaseSaveFileInput request) {

                throw outerServiceException(cause);
            }

            @Override
            public ReceivedPendingCaseOutput receptionPendingCase(String pendingCaseNo, ReceivedPendingCaseInput input) {
                throw outerServiceException(cause);
            }

            @Override
            public PendingCaseDeleteFileOutput deletePendingCaseFile(Long pendingCaseFileId, String pendingCaseNo) {
                throw outerServiceException(cause);
            }

            @Override
            public PendingCaseDetailResponse queryPendingCase(String pendingCaseNo) {
                throw outerServiceException(cause);
            }

            @Override
            public ClaimQueryCaseByCaseNoResponse queryClaimByApplicationNo(String applicationNo) {
                throw outerServiceException(cause);
            }

            @Override
            public QueryLiabilityListResponse queryLiabilityInfo(QueryLiabilityListRequest request) {
                throw outerServiceException(cause);
            }

            @Override
            public PageInfo<PolicyClaimApplicationApiResponse> queryClaimApplicationList(String policyNo, Integer pageSize, Integer pageIndex) {
                throw outerServiceException(cause);
            }

            @Override
            public SearchPaymentResponse searchPayment(SearchPayOrderRequest request) {
                throw outerServiceException(cause);
            }
        };
    }

    @Override
    public Class<? extends IErrorCode>[] mappingErrorCodes() {

        return new Class[] {OpenApiErrorCodes.class};
    }

    @Override
    public IErrorCode defaultErrorCode() {

        return OpenApiErrorCodes.SYSTEM_ERROR;
    }

    @Override
    public String outerServiceKey() {

        return "openapi";
    }

}
