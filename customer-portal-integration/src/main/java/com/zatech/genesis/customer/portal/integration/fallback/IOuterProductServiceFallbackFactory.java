package com.zatech.genesis.customer.portal.integration.fallback;

import com.zatech.genesis.customer.portal.integration.outer.product.IOuterProductService;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;
import com.zatech.genesis.product.api.structure.base.liability.QueryLiabilityListResponse;
import com.zatech.genesis.product.api.structure.request.QueryLiabilityListRequest;
import com.zatech.genesis.product.api.structure.request.QueryProductRequest;
import com.zatech.genesis.product.api.structure.response.ProductResponse;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/16
 */
@Slf4j
@Component
public class IOuterProductServiceFallbackFactory extends AbstractFallbackFactory<IOuterProductService> {

    @Override
    public IOuterProductService create(Throwable cause) {
        return new IOuterProductService() {
            @Override
            public List<QueryLiabilityListResponse> queryLiabilityListNew(QueryLiabilityListRequest requestDTO) {
                throw outerServiceException(cause);
            }

            @Override
            public ProductResponse queryProduct(QueryProductRequest requestDTO) {
                throw outerServiceException(cause);
            }
        };
    }
}
