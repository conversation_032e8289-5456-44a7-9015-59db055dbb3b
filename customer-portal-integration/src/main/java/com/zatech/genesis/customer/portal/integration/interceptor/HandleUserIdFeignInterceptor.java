package com.zatech.genesis.customer.portal.integration.interceptor;

import org.springframework.stereotype.Component;

import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * not send userId to other service
 */
@Component
public class HandleUserIdFeignInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.headers().keySet().stream().filter(x -> x.contains("octopus-user-id")).findFirst().ifPresent(key -> {
            requestTemplate.headers().remove(key);
        });
    }

}
