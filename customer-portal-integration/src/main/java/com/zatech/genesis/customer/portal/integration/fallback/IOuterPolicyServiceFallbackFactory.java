package com.zatech.genesis.customer.portal.integration.fallback;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.policy.DecisionTypeEnum;
import com.zatech.gaia.resource.components.enums.policymanagement.NBConfigurationTypeEnum;
import com.zatech.genesis.customer.portal.integration.outer.policy.IOuterPolicyService;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.policy.api.response.IssuanceRuleResponse;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.policy.api.response.proposal.ProposalDetailResponse;
import com.zatech.genesis.portal.toolbox.share.exception.PortalException;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Slf4j
@Component
public class IOuterPolicyServiceFallbackFactory implements FallbackFactory<IOuterPolicyService> {

    @Override
    public IOuterPolicyService create(Throwable cause) {
        HttpServletRequest request = getRequest();
        return new IOuterPolicyServiceImpl(request, cause);
    }

    private class IOuterPolicyServiceImpl implements IOuterPolicyService {
        private final HttpServletRequest request;

        private final Throwable cause;

        public IOuterPolicyServiceImpl(HttpServletRequest request, Throwable cause) {
            this.request = request;
            this.cause = cause;
        }

        @Override
        public IssuanceResponse getIssuance(String issuanceNo, String options, YesNoEnum temporary) throws PortalException {
            log.error(request.getPathTranslated());
            throw new PortalException("1") {
            };
        }

        @Override
        public ProposalDetailResponse getProposalDetail(String proposalNo, YesNoEnum temporary, Boolean queryCustomer) {
            return null;
        }

        @Override
        public PolicyResponse queryPolicy(String policyNo, String eventNo, Boolean queryCustomer) {
            return null;
        }

        @Override
        public List<IssuanceRuleResponse> queryIssuanceRules(String issuanceNo, List<DecisionTypeEnum> decisionTypes, List<NBConfigurationTypeEnum> ruleTypes) {

            return List.of();
        }

    }

    private HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            return requestAttributes.getRequest();
        }
        return null;
    }
}
