package com.zatech.genesis.customer.portal.integration.fallback;

import com.zatech.genesis.customer.portal.integration.outer.market.IOuterMarketService;
import com.zatech.genesis.market.api.structure.request.QueryGoodsRelatingRequest;
import com.zatech.genesis.market.api.structure.response.GoodsRelatingResponse;
import com.zatech.octopus.core.util.JacksonUtil;

import lombok.extern.slf4j.Slf4j;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/16
 */
@Slf4j
@Component
public class IOuterMarketServiceFallbackFactory implements FallbackFactory<IOuterMarketService> {
    private static final String ERROR = "\n <<[Method]: {}\n[request info]: {}\n[cause]: {}";

    @Override
    public IOuterMarketService create(Throwable cause) {
        return new IOuterMarketService() {
            @Override
            public GoodsRelatingResponse queryGoodsRelating(QueryGoodsRelatingRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCdcService.queryGoodsRelating", JacksonUtil.toJSONString(request), cause.getMessage());
                return null;
            }
        };
    }
}
