/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.integration.exception;

import com.zatech.genesis.portal.toolbox.exception.SpecificErrorCode;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @create 2023/9/21 17:53
 **/
public enum IntegrationErrorCodes implements IErrorCode {

    @SpecificErrorCode(status = HttpStatus.BAD_REQUEST, desc = "")
    QUERY_POLICY_INFO_FROM_CDC_ERROR,

    @SpecificErrorCode(status = HttpStatus.BAD_REQUEST, desc = "")
    QUERY_USER_INFO_FROM_CUSTOMER_ERROR,

    E_POLICY_GENERATING_ERROR,

    E_POLICY_GENERATED_ERROR,

    E_POLICY_POLICY_NO_NOT_EXIST_DOWNLOAD_ERROR,

    COMMON_REQUEST_UNSUPPORTED,

    USER_UNIQUE_ELEMENT_MISSING;

    public static final String MODULE_NAME = "integration";

    @Override
    public String getModuleName() {
        return MODULE_NAME;
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}
