package com.zatech.genesis.customer.portal.integration.fallback;

import com.zatech.gaia.resource.components.enums.doc.DocTypeEnum;
import com.zatech.gaia.resource.pendingcase.BusinessModuleEnum;
import com.zatech.genesis.customer.portal.integration.outer.notificaition.IOuterNotificationService;
import com.zatech.genesis.customer.portal.integration.outer.notificaition.response.NotificationDocFileResponse;
import com.zatech.genesis.customer.portal.integration.outer.notificaition.response.PendingCaseListResponse;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;
import com.zatech.octopus.common.dao.Page;

import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/16
 */
@Slf4j
@Component
public class IOuterNotificationFallbackFactory extends AbstractFallbackFactory<IOuterNotificationService> {
    private static final String ERROR = "\n <<[Method]: {}\n[request info]: {}\n[cause]: {}";

    @Override
    public IOuterNotificationService create(Throwable cause) {
        return new IOuterNotificationService() {

            @Override
            public Page<PendingCaseListResponse> listPendingCase(BusinessModuleEnum businessModule, String posNo, String claimCaseNo) {
                throw outerServiceException(cause);
            }

            @Override
            public Map<String, String> getAccessInfo(String token) {
                throw outerServiceException(cause);
            }

            @Override
            public List<NotificationDocFileResponse> queryDocFileByTransactionId(String businessNo, DocTypeEnum docType, Boolean latestOnly, Boolean relationalQuery) {
                throw outerServiceException(cause);
            }
        };
    }
}
