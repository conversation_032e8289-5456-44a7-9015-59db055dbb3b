package com.zatech.genesis.customer.portal.integration.outer.policy;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.schema.ObjectComponent;
import com.zatech.genesis.customer.portal.api.user.response.IssuanceDetailResponse;
import com.zatech.genesis.customer.portal.api.user.response.PolicyDetailResponse;
import com.zatech.genesis.customer.portal.integration.outer.market.OuterMarketServiceAdapter;
import com.zatech.genesis.customer.portal.integration.outer.product.OuterProductServiceAdapter;
import com.zatech.genesis.market.api.structure.base.schema.type.ObjectElement;
import com.zatech.genesis.market.api.structure.base.schema.type.ObjectElementBase;
import com.zatech.genesis.market.api.structure.response.GoodsRelatingResponse;
import com.zatech.genesis.market.api.structure.response.PackageInfoResponse;
import com.zatech.genesis.market.api.structure.response.PackageSchemaApplicationElementsResponse;
import com.zatech.genesis.policy.api.base.IssuanceAttachmentBase;
import com.zatech.genesis.policy.api.response.*;
import com.zatech.genesis.policy.api.response.object.auto.IssuanceAdditionalEquipmentResponse;
import com.zatech.genesis.policy.api.response.object.auto.PolicyAdditionalEquipmentResponse;
import com.zatech.genesis.policy.api.response.proposal.ProposalDetailResponse;
import com.zatech.genesis.policy.api.response.proposal.ProposalInsuredResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import jakarta.validation.constraints.NotNull;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023-10-25 11:37
 */
@ExtendWith(MockitoExtension.class)
class OuterPolicyServiceAdapterTest {

    @InjectMocks
    private OuterPolicyServiceAdapter adapter;

    @Mock
    private IOuterPolicyService outerPolicyService;

    @Mock
    public OuterProductServiceAdapter productServiceAdapter;

    @Mock
    public OuterMarketServiceAdapter outerMarketServiceAdapter;

    @Test
    void test_getProposalDetail() {
        String proposalNo = "proposalNo";
        ProposalDetailResponse res = new ProposalDetailResponse();
        List<IssuanceAttachmentBase> attachmentBases = new ArrayList<>();
        IssuanceAttachmentBase attachmentBase = new IssuanceAttachmentBase();
        attachmentBase.setAttachmentUrl("url");
        attachmentBases.add(attachmentBase);
        res.setIssuanceAttachmentList(attachmentBases);
        res.setIssuanceProductList(Arrays.asList(new IssuanceProductResponse()));
        List<ProposalInsuredResponse> proposalInsurantList = new ArrayList<>();
        ProposalInsuredResponse insure = new ProposalInsuredResponse();
        insure.setCertiNo("certiNo");
        proposalInsurantList.add(insure);
        res.setProposalInsurantList(proposalInsurantList);
        List<IssuanceInsuredObjectResponse> issuanceInsuredObjectResponseList = new ArrayList<>();
        res.setIssuanceInsuredObjectList(issuanceInsuredObjectResponseList);
        IssuanceInsuredObjectResponse issuanceInsuredObjectResponse = new IssuanceInsuredObjectResponse();
        issuanceInsuredObjectResponse.setInsuredNo("insuredNo");
        IssuanceInsuredOrderResponse issuanceInsuredOrder = new IssuanceInsuredOrderResponse();
        issuanceInsuredOrder.setBookingNumber("bookingNumber");
        issuanceInsuredObjectResponse.setIssuanceInsuredOrder(issuanceInsuredOrder);
        IssuanceInsuredAutoResponse issuanceInsuredAuto = new IssuanceInsuredAutoResponse();
        IssuanceInsuredAutoCarOwnerResponse carOwner = new IssuanceInsuredAutoCarOwnerResponse();
        carOwner.setCustomerType("customerType");
        issuanceInsuredAuto.setCarOwner(carOwner);
        List<IssuanceInsuredAutoDriverResponse> driverResponseList = new ArrayList<>();
        IssuanceInsuredAutoDriverResponse issuanceInsuredAutoDriverResponse = new IssuanceInsuredAutoDriverResponse();
        issuanceInsuredAutoDriverResponse.setCertiNo("1");
        IssuanceInsuredAutoDriverResponse issuanceInsuredAutoDriverResponse1 = new IssuanceInsuredAutoDriverResponse();
        issuanceInsuredAutoDriverResponse1.setCertiNo("2");
        driverResponseList.add(issuanceInsuredAutoDriverResponse);
        driverResponseList.add(issuanceInsuredAutoDriverResponse1);
        issuanceInsuredAuto.setIssuanceInsuredAutoDriverList(driverResponseList);
        List<IssuanceAdditionalEquipmentResponse> issuanceAdditionalEquipmentResponseList = new ArrayList<>();
        IssuanceAdditionalEquipmentResponse issuanceAdditionalEquipmentResponse = new IssuanceAdditionalEquipmentResponse();
        issuanceAdditionalEquipmentResponse.setEquipmentName("setEquipmentName");
        IssuanceAdditionalEquipmentResponse issuanceAdditionalEquipmentResponse1 = new IssuanceAdditionalEquipmentResponse();
        issuanceAdditionalEquipmentResponse.setEquipmentName("setEquipmentName1");
        issuanceAdditionalEquipmentResponseList.add(issuanceAdditionalEquipmentResponse);
        issuanceAdditionalEquipmentResponseList.add(issuanceAdditionalEquipmentResponse1);
        issuanceInsuredAuto.setIssuanceAdditionalEquipmentList(issuanceAdditionalEquipmentResponseList);
        List<IssuanceInsuredAutoInspectionResponse> issuanceInsuredAutoInspectionList = new ArrayList<>();
        IssuanceInsuredAutoInspectionResponse issuanceInsuredAutoInspectionResponse = new IssuanceInsuredAutoInspectionResponse();
        issuanceInsuredAutoInspectionResponse.setPlatform("setPlatform");
        IssuanceInsuredAutoInspectionResponse issuanceInsuredAutoInspectionResponse1 = new IssuanceInsuredAutoInspectionResponse();
        issuanceInsuredAutoInspectionResponse1.setPlatform("setPlatform1");
        issuanceInsuredAutoInspectionList.add(issuanceInsuredAutoInspectionResponse);
        issuanceInsuredAutoInspectionList.add(issuanceInsuredAutoInspectionResponse1);
        issuanceInsuredAuto.setIssuanceInsuredAutoInspectionList(issuanceInsuredAutoInspectionList);
        IssuanceInsuredAutoOtherInfoResponse otherInfo = new IssuanceInsuredAutoOtherInfoResponse();
        otherInfo.setExaminationDetail("setExaminationDetail");
        issuanceInsuredAuto.setOtherInfo(otherInfo);
    
        IssuanceInsuredAutoLoanResponse issuanceInsuredAutoLoanResponse = new IssuanceInsuredAutoLoanResponse();
        issuanceInsuredAutoLoanResponse.setLoanCompany("setLoanCompany");
        issuanceInsuredAuto.setLoan(issuanceInsuredAutoLoanResponse);
        
        issuanceInsuredObjectResponse.setIssuanceInsuredAuto(issuanceInsuredAuto);
    
        issuanceInsuredObjectResponseList.add(issuanceInsuredObjectResponse);
        
        when(outerPolicyService.getProposalDetail(anyString(), any(), anyBoolean())).thenReturn(res);

        when(outerMarketServiceAdapter.queryGoodsPackageApplicationElements(any())).thenReturn(getGoodsRelatingResponse());

        IssuanceDetailResponse response = adapter.getProposalDetail(proposalNo, YesNoEnum.NO);
        Assertions.assertNotNull(response);
    }

    @NotNull
    private GoodsRelatingResponse getGoodsRelatingResponse() {
        GoodsRelatingResponse goodsRelatingResponse = new GoodsRelatingResponse();
        List<PackageInfoResponse> packageInfoResponseList = new ArrayList<>();
        goodsRelatingResponse.setPackageList(packageInfoResponseList);

        PackageInfoResponse packageInfoResponse = new PackageInfoResponse();
        packageInfoResponseList.add(packageInfoResponse);

        PackageSchemaApplicationElementsResponse packageSchemaApplicationElementsResponse = new PackageSchemaApplicationElementsResponse();
        packageInfoResponse.setPackageSchemaApplicationElements(packageSchemaApplicationElementsResponse);

        List<ObjectElementBase> objectElementList = new ArrayList<>();
        packageSchemaApplicationElementsResponse.setObjectElementList(objectElementList);
        ObjectElementBase objectElementBase = new ObjectElementBase();
        objectElementList.add(objectElementBase);
        objectElementBase.setObjectComponent(ObjectComponent.ORDER);
        objectElementBase.setCode("bookingNumber");


        List<ObjectElement> objectElements = new ArrayList<>();
        packageSchemaApplicationElementsResponse.setObjectElements(objectElements);
        ObjectElement objectElement = new ObjectElement();
        objectElement.setObjectComponent(ObjectComponent.DRIVER);
        objectElement.setCode("certiNo");
        objectElements.add(objectElement);

        ObjectElement objectElement1 = new ObjectElement();
        objectElement1.setObjectComponent(ObjectComponent.VEHICLE_ADDITIONAL_EQUIPMENT);
        objectElement1.setCode("equipmentName");
        objectElements.add(objectElement1);

        ObjectElement objectElement2 = new ObjectElement();
        objectElement2.setObjectComponent(ObjectComponent.VEHICLE_LOAN);
        objectElement2.setCode("loanCompany");
        objectElements.add(objectElement2);
        return goodsRelatingResponse;
    }

    @Test
    void test_queryPolicy() {
        String policyNo = "policyNo";
        PolicyResponse res = new PolicyResponse();
        PolicyProductResponse productResponse = new PolicyProductResponse();
        productResponse.setPolicyProductPremiumList(Arrays.asList(new PolicyProductPremiumResponse()));
        productResponse.setPolicyProductLiabilityList(Arrays.asList(new PolicyProductLiabilityResponse()));
        res.setPolicyProductList(Arrays.asList(productResponse));
    
        List<PolicyInsuredObjectResponse> policyInsuredObjectList = new ArrayList<>();
        res.setPolicyInsuredObjectList(policyInsuredObjectList);
        PolicyInsuredObjectResponse policyInsuredObjectResponse = new PolicyInsuredObjectResponse();
        policyInsuredObjectResponse.setInsuredNo("insuredNo");
        PolicyInsuredOrderResponse policyInsuredOrderResponse = new PolicyInsuredOrderResponse();
        policyInsuredOrderResponse.setBookingNumber("bookingNumber");
        policyInsuredObjectResponse.setPolicyInsuredOrder(policyInsuredOrderResponse);
    
        PolicyInsuredAutoResponse policyInsuredAutoResponse = new PolicyInsuredAutoResponse();
        PolicyInsuredAutoCarOwnerResponse carOwner = new PolicyInsuredAutoCarOwnerResponse();
        carOwner.setCustomerType("customerType");
        policyInsuredAutoResponse.setCarOwner(carOwner);
        List<PolicyInsuredAutoDriverResponse> driverResponseList = new ArrayList<>();
        PolicyInsuredAutoDriverResponse policyInsuredAutoDriverResponse = new PolicyInsuredAutoDriverResponse();
        policyInsuredAutoDriverResponse.setCertiNo("1");
        PolicyInsuredAutoDriverResponse policyInsuredAutoDriverResponse1 = new PolicyInsuredAutoDriverResponse();
        policyInsuredAutoDriverResponse1.setCertiNo("2");
        driverResponseList.add(policyInsuredAutoDriverResponse);
        driverResponseList.add(policyInsuredAutoDriverResponse1);
        policyInsuredAutoResponse.setPolicyInsuredAutoDriverList(driverResponseList);
        List<PolicyAdditionalEquipmentResponse> policyAdditionalEquipmentResponses = new ArrayList<>();
        PolicyAdditionalEquipmentResponse policyAdditionalEquipmentResponse = new PolicyAdditionalEquipmentResponse();
        policyAdditionalEquipmentResponse.setEquipmentName("setEquipmentName");
        PolicyAdditionalEquipmentResponse policyAdditionalEquipmentResponse1 = new PolicyAdditionalEquipmentResponse();
        policyAdditionalEquipmentResponse.setEquipmentName("setEquipmentName1");
        policyAdditionalEquipmentResponses.add(policyAdditionalEquipmentResponse);
        policyAdditionalEquipmentResponses.add(policyAdditionalEquipmentResponse1);
        policyInsuredAutoResponse.setPolicyAdditionalEquipmentList(policyAdditionalEquipmentResponses);
        List<PolicyInsuredAutoInspectionResponse> policyInsuredAutoInspectionList = new ArrayList<>();
        PolicyInsuredAutoInspectionResponse policyInsuredAutoInspectionResponse = new PolicyInsuredAutoInspectionResponse();
        policyInsuredAutoInspectionResponse.setPlatform("setPlatform");
        PolicyInsuredAutoInspectionResponse policyInsuredAutoInspectionResponse1 = new PolicyInsuredAutoInspectionResponse();
        policyInsuredAutoInspectionResponse1.setPlatform("setPlatform1");
        policyInsuredAutoInspectionList.add(policyInsuredAutoInspectionResponse);
        policyInsuredAutoInspectionList.add(policyInsuredAutoInspectionResponse1);
        policyInsuredAutoResponse.setPolicyInsuredAutoInspectionList(policyInsuredAutoInspectionList);
        PolicyInsuredAutoOtherInfoResponse otherInfo = new PolicyInsuredAutoOtherInfoResponse();
        otherInfo.setExaminationDetail("setExaminationDetail");
        policyInsuredAutoResponse.setOtherInfo(otherInfo);
    
        PolicyInsuredAutoLoanResponse policyInsuredAutoLoanResponse = new PolicyInsuredAutoLoanResponse();
        policyInsuredAutoLoanResponse.setLoanCompany("setLoanCompany");
        policyInsuredAutoResponse.setLoan(policyInsuredAutoLoanResponse);
        
        policyInsuredObjectResponse.setPolicyInsuredAuto(policyInsuredAutoResponse);
        policyInsuredObjectList.add(policyInsuredObjectResponse);

        when(outerMarketServiceAdapter.queryGoodsPackageApplicationElements(any())).thenReturn(getGoodsRelatingResponse());
        when(outerPolicyService.queryPolicy(anyString(), anyString(), anyBoolean())).thenReturn(res);

        PolicyDetailResponse response = adapter.queryPolicy(policyNo, "eventNo");
        Assertions.assertNotNull(response);
    }

}
