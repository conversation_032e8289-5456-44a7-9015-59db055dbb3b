package com.zatech.genesis.customer.portal.integration.exception;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 * @date 2023-10-25 13:59
 */
@ExtendWith(MockitoExtension.class)
class IntegrationErrorCodesTest {

    @Test
    void test_getModuleName() {
        String name = IntegrationErrorCodes.QUERY_POLICY_INFO_FROM_CDC_ERROR.getModuleName();
        Assertions.assertNotNull(name);
    }

    @Test
    void test_getErrorCode() {
        String code = IntegrationErrorCodes.QUERY_USER_INFO_FROM_CUSTOMER_ERROR.getErrorCode();
        Assertions.assertNotNull(code);
    }

}
