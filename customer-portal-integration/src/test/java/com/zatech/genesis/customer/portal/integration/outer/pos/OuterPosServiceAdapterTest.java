package com.zatech.genesis.customer.portal.integration.outer.pos;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.genesis.customer.portal.api.user.response.PolicyPosItemsResponse;
import com.zatech.genesis.customer.portal.integration.outer.policy.IOuterPolicyService;
import com.zatech.genesis.customer.portal.integration.outer.pos.response.PolicyPosItemResponse;
import com.zatech.genesis.customer.portal.integration.outer.product.IOuterProductService;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;

public class OuterPosServiceAdapterTest {

    @InjectMocks
    private OuterPosServiceAdapter outerPosServiceAdapter;

    @Mock
    private IOuterPosService outerPosService;

    @Mock
    private IOuterPolicyService policyService;

    @Mock
    private IOuterProductService productService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void queryPosItems() {
        PolicyPosItemResponse policyPosItemResponse = new PolicyPosItemResponse();
        policyPosItemResponse.setDisabled(Boolean.FALSE);
        policyPosItemResponse.setTransType(TransTypeEnum.POS_CANCELLATION);
        Mockito.when(outerPosService.listPosItems(Mockito.any())).thenReturn(Arrays.asList(policyPosItemResponse));
        List<PolicyPosItemsResponse> items = outerPosServiceAdapter.queryPosItems("B1123");
        Assertions.assertNotNull(items);
    }
}