package com.zatech.genesis.customer.portal.integration.outer.cdc;

import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.genesis.cdc.es.client.search.builder.SearchSourceBuilder;
import com.zatech.genesis.customer.portal.integration.outer.cdc.request.PolicyQueryRequest;
import com.zatech.genesis.customer.portal.integration.outer.cdc.request.ProposalQueryRequest;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.octopus.module.web.dto.ResultBaseExt;
import com.zhongan.graphene.cdc.scenario.share.dto.base.EsPageResponseDTO;
import com.zhongan.graphene.cdc.scenario.share.dto.response.issuance.IssuanceResponseDTO;
import com.zhongan.graphene.cdc.scenario.share.dto.response.policy.PolicyResponseDTO;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023-10-25 11:52
 */
@ExtendWith(MockitoExtension.class)
class OuterCdcServiceAdapterTest {

    @InjectMocks
    private OuterCdcServiceAdapter adapter;

    @Mock
    private IOuterCdcService outerCdcService;

    @Mock
    private com.zatech.genesis.customer.portal.plugin.share.userpolicy.IUserPolicyServiceExtensionPoint userPolicyServiceExtensionPoint;

    @Test
    void test_getPolicyInfoByPersonId_success() {

        EsPageResponseDTO<PolicyResponseDTO> esPageResponse = new EsPageResponseDTO();
        esPageResponse.setTotalHit(1L);
        when(outerCdcService.searchPolicyNewList(any())).thenReturn(ResultBaseExt.success(esPageResponse));
        EsPageResponseDTO<PolicyResponseDTO> result = adapter.getPolicyInfoByPersonId(1L);
        Assertions.assertNotNull(result);
    }

    @Test
    void test_getPolicyInfoByPersonId_fail() {

        when(outerCdcService.searchPolicyNewList(any())).thenReturn(ResultBaseExt.fail("1"));
        Assertions.assertThrows(CommonException.class, () -> adapter.getPolicyInfoByPersonId(1L));
    }

    @Test
    void test_existProposalInfoByUser() {

        Page<IssuanceResponseDTO> page = Mockito.mock(Page.class);
        Mockito.when(page.getTotalElements()).thenReturn(1L);
        when(userPolicyServiceExtensionPoint.generateQueryCDCBuilder(any(), any())).thenReturn(new SearchSourceBuilder());

        when(outerCdcService.queryIssuanceList(any())).thenReturn(page);
        ProposalQueryRequest policyQueryRequest = new ProposalQueryRequest();
        policyQueryRequest.setIssuanceNo("1");
        policyQueryRequest.setUniqueElementIntegrity(true);
        policyQueryRequest.setPartyType(PartyTypeEnum.INDIVIDUAL);

        boolean result = adapter.existProposalInfoByUser(policyQueryRequest);
        Assertions.assertTrue(result);
    }

    @Test
    void test_existPolicyInfoByUser() {

        Page<PolicyResponseDTO> page = Mockito.mock(Page.class);
        Mockito.when(page.getTotalElements()).thenReturn(1L);
        when(userPolicyServiceExtensionPoint.generateQueryCDCBuilder(any(), any())).thenReturn(new SearchSourceBuilder());

        when(outerCdcService.queryPolicyList(any())).thenReturn(page);
        PolicyQueryRequest policyQueryRequest = new PolicyQueryRequest();
        policyQueryRequest.setPolicyNo("1");
        policyQueryRequest.setPartyType(PartyTypeEnum.INDIVIDUAL);

        boolean result = adapter.existPolicyInfoByUser(policyQueryRequest);
        Assertions.assertTrue(result);
    }

    @Test
    void test_getProposalInfoByUser_success() {
        when(userPolicyServiceExtensionPoint.generateQueryCDCBuilder(any(), any())).thenReturn(new SearchSourceBuilder());

        ProposalQueryRequest response = new ProposalQueryRequest();
        response.setName("123").setCertiNo("999").setCertiType(CertiTypeEnum.CMND);
        response.setUniqueElementIntegrity(Boolean.TRUE);
        response.setPartyType(PartyTypeEnum.INDIVIDUAL);
        adapter.getProposalInfoByUser(response);
        verify(outerCdcService, times(1)).queryIssuanceList(any());
    }

}
