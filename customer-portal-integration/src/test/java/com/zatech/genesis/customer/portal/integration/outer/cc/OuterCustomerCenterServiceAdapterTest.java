package com.zatech.genesis.customer.portal.integration.outer.cc;

import com.zatech.genesis.customer.portal.integration.outer.cc.response.CcInfoResponse;
import com.zatech.genesis.portal.toolbox.exception.CommonException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023-10-25 11:37
 */
@ExtendWith(MockitoExtension.class)
class OuterCustomerCenterServiceAdapterTest {

    @InjectMocks
    private OuterCustomerCenterServiceAdapter adapter;

    @Mock
    private IOuterCustomerCenterService outerCustomerCenterService;

    @Test
    void test_getChannelCustomerInfo_success() {
        when(outerCustomerCenterService.getChannelCustomerInfo(anyLong())).thenReturn(new CcInfoResponse());
        CcInfoResponse response = adapter.getChannelCustomerInfo(1L);
        Assertions.assertNotNull(response);
    }

    @Test
    void test_getChannelCustomerInfo_fail() {
        when(outerCustomerCenterService.getChannelCustomerInfo(anyLong())).thenReturn(null);
        Assertions.assertThrows(CommonException.class, () -> adapter.getChannelCustomerInfo(1L));
    }

}
