/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.integration.outer.market;

import com.zatech.genesis.customer.portal.api.market.request.QueryGoodsRequest;
import com.zatech.genesis.market.api.structure.base.I18nMessageBase;
import com.zatech.genesis.market.api.structure.response.GoodsBasicInfoResponse;
import com.zatech.genesis.market.api.structure.response.GoodsCoveragePlanResponse;
import com.zatech.genesis.market.api.structure.response.GoodsRelatingResponse;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @create 2025/3/14 14:12
 */
@ExtendWith(MockitoExtension.class)
public class OuterMarketServiceAdapterTest {

    @InjectMocks
    private OuterMarketServiceAdapter outerMarketServiceAdapter;

    @Mock
    private IOuterMarketService outerMarketService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void test_query_goods() {
        var goodsRelatingResponse = new GoodsRelatingResponse();
        goodsRelatingResponse.setGoodsId(1L);
        goodsRelatingResponse.setGoodsBasicInfo(new GoodsBasicInfoResponse());
        var goodsCoveragePlanResponse = new GoodsCoveragePlanResponse();
        goodsCoveragePlanResponse.setGoodsId(1L);
        var i18 = new I18nMessageBase();
        i18.setLang("lang");
        goodsCoveragePlanResponse.setI18nPlanName(List.of(i18));
        goodsRelatingResponse.setCoveragePlans(List.of(goodsCoveragePlanResponse));
        Mockito.when(outerMarketService.queryGoodsRelating(any())).thenReturn(goodsRelatingResponse);
        var queryGoodsRequest = new QueryGoodsRequest();
        queryGoodsRequest.setGoodsId(1L);
        var goodsResponse = outerMarketServiceAdapter.queryGoodsRelating(queryGoodsRequest);
        Assertions.assertNotNull(goodsResponse);
    }
}
