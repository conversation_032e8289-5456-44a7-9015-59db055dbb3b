<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zatech.genesis</groupId>
        <artifactId>customer-portal</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>customer-portal-integration</artifactId>
    <name>customer-portal-integration</name>
    <description>Customer Portal Integration Module</description>
    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>customer-portal-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>customer-portal-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-cdc-es-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-policy-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-market-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-product-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-cdc-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>octopus-spring-boot-starter-circuitbreaker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>portal-baseline-plugin-share</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech</groupId>
            <artifactId>openapi-sdk-core</artifactId>
            <version>1.0.8-preview</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>integration-gateway-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-metadata-i18n-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>lowcode-framework-api</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <pomElements>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>
