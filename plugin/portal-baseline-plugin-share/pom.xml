<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zatech.genesis</groupId>
        <artifactId>customer-portal</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>portal-baseline-plugin-share</artifactId>
    <description>customer portal plugin baseline plugin share</description>
    <packaging>jar</packaging>
    <properties>
        <java.version>17</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.zhongan.pluginframework</groupId>
            <artifactId>plugin-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.pluginframework</groupId>
            <artifactId>plugin-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-octopus-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-cdc-es-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>customer-portal-share</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-cdc-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-policy-api</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <pomElements>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>
