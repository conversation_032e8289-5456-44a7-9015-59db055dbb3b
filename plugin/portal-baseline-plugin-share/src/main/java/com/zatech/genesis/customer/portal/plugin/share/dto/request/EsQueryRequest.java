/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.plugin.share.dto.request;

import com.zatech.gaia.resource.claim.CustomerTypeEnum;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.gaia.resource.components.enums.schema.OrganizationIDTypeEnum;
import com.zatech.genesis.policy.api.reqeust.BasePageQueryRequest;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

import lombok.Data;

/**
 * Description:
 *
 * @version 1.0
 * @date 2025/2/26 14:06
 */
@Data
public class EsQueryRequest extends BasePageQueryRequest implements Serializable {

    private static final long serialVersionUID = -8451214583092818289L;

    private String name;

    private String certiNo;

    private CertiTypeEnum certiType;

    private PartyTypeEnum partyType;

    private long personId;

    private long customerId;

    private GenderEnum gender;

    private LocalDate birthday;

    private String email;

    private String phone;

    private String countryCode;

    private String zipCode;

    private boolean uniqueElementIntegrity;

    private String policyNo;

    private String issuanceNo;

    private Set<Integer> issuanceStatus;

    private Set<String> policyStatus;

    private List<ChannelRelationRequest> channelRelationRequestList;

    private String zoneId;

    private CustomerTypeEnum customerType = CustomerTypeEnum.POLICY_HOLDER;

    private String organizationIdNo;

    private OrganizationIDTypeEnum organizationIdType;

    private InsuredQueryRequest insuredQueryRequest;

}
