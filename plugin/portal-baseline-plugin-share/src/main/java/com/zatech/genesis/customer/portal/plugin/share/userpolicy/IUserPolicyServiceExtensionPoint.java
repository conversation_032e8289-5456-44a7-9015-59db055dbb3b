/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.plugin.share.userpolicy;

import com.zatech.genesis.cdc.es.client.search.builder.SearchSourceBuilder;
import com.zatech.genesis.customer.portal.plugin.share.constant.InquireUserPolicySourceEnum;
import com.zatech.genesis.customer.portal.plugin.share.dto.request.EsQueryRequest;
import com.zatech.genesis.customer.portal.plugin.share.dto.request.PolicyClauseFilePluginRequest;
import com.zatech.genesis.customer.portal.plugin.share.dto.response.PolicyClauseFilePluginResponse;
import com.zhongan.pluginframework.ExtensionPoint;

/**
 * Description: 用于管理通过Customer Portal检索保单时，部分场景的扩充Plugin
 *
 * @version 1.0
 * @date 2025/2/26 09:55
 */
public interface IUserPolicyServiceExtensionPoint extends ExtensionPoint {

    /**
     * 构建的CDC检索条件
     * 可灵活的构建Builder和自定义处理CDC检索的属性对象及值
     * person id 检索或kyc标识检索
     *
     * @param request                     Integration中已经封装好的对象，可解析此数据构建SearchSourceBuilder
     * @param inquireUserPolicySourceEnum 使用来源
     * @return
     */
    SearchSourceBuilder generateQueryCDCBuilder(EsQueryRequest request, InquireUserPolicySourceEnum inquireUserPolicySourceEnum);


    /**
     * 将原生的持久化的约款信息以file_unique_code 可替换为filePath的扩展性管理
     *
     * @param policyClauseFilePluginRequest 约款集合信息 #filePath 、fileUniqueCode 、docName 、docType
     * @return
     */
    PolicyClauseFilePluginResponse convertPolicyClauseFileToUrl(PolicyClauseFilePluginRequest policyClauseFilePluginRequest);

}
