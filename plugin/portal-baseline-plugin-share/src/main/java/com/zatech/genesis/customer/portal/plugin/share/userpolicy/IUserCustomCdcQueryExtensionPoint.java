package com.zatech.genesis.customer.portal.plugin.share.userpolicy;

import com.zatech.genesis.customer.portal.plugin.share.dto.request.EsQueryRequest;
import com.zhongan.graphene.cdc.scenario.share.dto.response.policy.PolicyResponseDTO;
import com.zhongan.pluginframework.ExtensionPoint;

import java.util.List;

import org.springframework.data.domain.Page;

/**
 *
 * 用于用户自定义cdc查询方式
 *
 * <AUTHOR>
 * @date 2025/3/20 15:01
 **/
public interface IUserCustomCdcQueryExtensionPoint extends ExtensionPoint {

    Page<PolicyResponseDTO> searchPolicyResponseDTO(EsQueryRequest request);


}
