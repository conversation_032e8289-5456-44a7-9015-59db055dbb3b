/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.plugin.share.dto.response;

import com.zatech.gaia.resource.components.enums.policy.PolicyFileSourceTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Data;

/**
 * Description: 返回的明细 如需要返回一个 public URL 需要处理filePath属性
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/13 14:30
 */
@Data
public class PolicyClauseFilePluginResponse implements Serializable {

    private static final long serialVersionUID = 2753167645293784473L;

    /**
     * The unique identifier of the policy clause file.
     */
    private Long policyClauseFileId;


    /**
     * This is a URL path that accesses it.
     */
    private String filePath;

    /**
     * The unique code assigned to a file or document related to an insurance claim or policy.
     */
    private String fileUniqueCode;


    /**
     * The name of the document associated with the policy
     */
    private String docName;

    /**
     * The type of document associated with a policy or transaction.
     */
    private String docType;


    /**
     * The type of source (e.g., internal, external) for the policy or event.
     */
    private PolicyFileSourceTypeEnum sourceType;

    /**
     * The unique identifier of the source of the policy or event.
     */
    private Long sourceId;

    /**
     * The version of the policy or record in an insurance policy.
     */
    private String version;


    /**
     * The language used for a specific field or entity.
     */
    private String language;

}
