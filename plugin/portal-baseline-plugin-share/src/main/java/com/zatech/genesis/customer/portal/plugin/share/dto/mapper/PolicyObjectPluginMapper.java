/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.plugin.share.dto.mapper;


import com.zatech.genesis.customer.portal.plugin.share.dto.request.PolicyClauseFilePluginRequest;
import com.zatech.genesis.customer.portal.plugin.share.dto.response.PolicyClauseFilePluginResponse;

/**
 * Description: 大对象属性的快捷转换工具
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/13 15:20
 */
public class PolicyObjectPluginMapper {


    /**
     * 默认行为的结构转换 将约款的入参转换为出参行为
     * @param policyClauseFilePluginRequest 约款请求的plugin数据对象
     * @return 返回 默认的约款plugin 对象属性内容
     */
    public static PolicyClauseFilePluginResponse convertToPluginPolicyClauseFile(PolicyClauseFilePluginRequest policyClauseFilePluginRequest) {
        if (null == policyClauseFilePluginRequest) {
            return new PolicyClauseFilePluginResponse();
        }
        PolicyClauseFilePluginResponse policyClauseFilePluginResponse = new PolicyClauseFilePluginResponse();
        policyClauseFilePluginResponse.setFilePath(null);
        policyClauseFilePluginResponse.setFileUniqueCode(policyClauseFilePluginRequest.getFileUniqueCode());
        policyClauseFilePluginResponse.setDocName(policyClauseFilePluginRequest.getDocName());
        policyClauseFilePluginResponse.setDocType(policyClauseFilePluginRequest.getDocType());
        policyClauseFilePluginResponse.setPolicyClauseFileId(policyClauseFilePluginResponse.getPolicyClauseFileId());
        policyClauseFilePluginResponse.setLanguage(policyClauseFilePluginResponse.getLanguage());
        policyClauseFilePluginResponse.setVersion(policyClauseFilePluginResponse.getVersion());
        policyClauseFilePluginResponse.setSourceId(policyClauseFilePluginResponse.getSourceId());
        policyClauseFilePluginResponse.setSourceType(policyClauseFilePluginRequest.getSourceType());
        return policyClauseFilePluginResponse;
    }


}
