/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.plugin.share.constant;

/**
 * 用于区分在OuterCdcServiceAdapter 结构中调用CDC中不同已提供方法的来源
 */
public enum InquireUserPolicySourceEnum {

    EXIST_PROPOSAL_INFO,
    /**
     * getProposalListByUser
     */
    PROPOSAL_LIST,
    /**
     * existPolicy
     */
    EXIST_POLICY_INFO,

    /**
     * getPolicyInfoByUser
     */
    POLICY_LIST
}
