<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zatech.genesis</groupId>
        <artifactId>customer-portal</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>customer-portal-pos</artifactId>
    <description>customer portal plugin pos baseline</description>
    <properties>
        <java.version>17</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>octopus-spring-boot-starter-rpc-openfeign</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>toolbox-exception</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>zatech-resourcecode-enum-gaia</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>lowcode-framework-client-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>toolbox-jsonschema</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-market-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-pos-online-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.pluginframework</groupId>
            <artifactId>plugin-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.pluginframework</groupId>
            <artifactId>plugin-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-octopus-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>lowcode-framework-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>swagger-annotations-jakarta</artifactId>
                    <groupId>io.swagger.core.v3</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-models-jakarta</artifactId>
                    <groupId>io.swagger.core.v3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-policy-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>customer-portal-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>customer-portal-biz-common</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <pomElements>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

</project>
