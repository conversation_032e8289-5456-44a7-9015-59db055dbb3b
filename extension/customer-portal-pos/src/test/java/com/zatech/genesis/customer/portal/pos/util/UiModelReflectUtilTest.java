/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zatech.gaia.resource.components.enums.schema.CustomerSubTypeEnum;
import com.zatech.genesis.customer.portal.pos.outer.market.response.ApplicationElementItemsResponse;
import com.zatech.genesis.customer.portal.pos.outer.market.response.ApplicationElementsItem;
import com.zatech.genesis.customer.portal.pos.outer.market.response.EnumItem;
import com.zatech.genesis.customer.portal.pos.outer.response.PosItemConfigResponse;
import com.zatech.genesis.customer.portal.pos.template.IndividualCustomerElement;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;
import com.zatech.octopus.core.util.JacksonUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertNotNull;

public class UiModelReflectUtilTest {

    @InjectMocks
    private UiModelReflectUtil uiModelReflectUtil;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testConvertObjectFromPosConfig() {
        // 创建测试数据
        Object target = new IndividualCustomerElement.AddressTemplate();
        String json = "[{\"posItemName\":\"被保人基本信息变更\",\"categoryCode\":\"POLICY_PARTY_INFO_CHANGE\",\"itemCode\":\"POS_INSURED_INFO_CHANGES\",\"posConfigStatus\":\"EFFECTIVE\",\"posIcon\":\"insuredObject7\",\"description\":null,\"posItemModules\":[{\"moduleCode\":\"posReason\",\"required\":\"YES\",\"customerType\":null,\"customerSubType\":null,\"schemaDefType\":null,\"posItemModuleProperties\":[{\"propertyName\":\"Others\",\"propertyCode\":\"111\",\"enumValue\":\"111\",\"enumName\":null,\"orderNo\":2,\"selected\":\"YES\",\"enumFlag\":\"NO\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"Others\"},{\"propertyName\":\"W002\",\"propertyCode\":\"123\",\"enumValue\":\"123\",\"enumName\":null,\"orderNo\":3,\"selected\":\"YES\",\"enumFlag\":\"NO\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"W002\"},{\"propertyName\":\"NO Reason\",\"propertyCode\":\"1\",\"enumValue\":\"1\",\"enumName\":null,\"orderNo\":4,\"selected\":\"YES\",\"enumFlag\":\"NO\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"NO Reason\"},{\"propertyName\":\"TEST Reason\",\"propertyCode\":\"2\",\"enumValue\":\"2\",\"enumName\":null,\"orderNo\":5,\"selected\":\"YES\",\"enumFlag\":\"NO\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"TEST Reason\"},{\"propertyName\":\"a002\",\"propertyCode\":\"333\",\"enumValue\":\"333\",\"enumName\":null,\"orderNo\":6,\"selected\":\"YES\",\"enumFlag\":\"NO\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"a002\"},{\"propertyName\":\"h001\",\"propertyCode\":\"444\",\"enumValue\":\"444\",\"enumName\":null,\"orderNo\":7,\"selected\":\"YES\",\"enumFlag\":\"NO\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"h001\"}]},{\"moduleCode\":\"trialCalculationPerformedModule\",\"required\":\"NO\",\"customerType\":null,\"customerSubType\":null,\"schemaDefType\":null,\"posItemModuleProperties\":[{\"propertyName\":\"Trial calculation must be performed to proceed to the next step\",\"propertyCode\":\"trialCalculationPerformed\",\"enumValue\":\"trialCalculationPerformed\",\"enumName\":null,\"orderNo\":1,\"selected\":\"YES\",\"enumFlag\":\"NO\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"Trial calculation must be performed to proceed to the next step\"}]},{\"moduleCode\":\"posEffectiveDateType\",\"required\":\"YES\",\"customerType\":null,\"customerSubType\":null,\"schemaDefType\":null,\"posItemModuleProperties\":[{\"propertyName\":\"保单生效日\",\"propertyCode\":\"policyEffectiveDate\",\"enumValue\":\"POLICY_EFFECTIVE_DATE\",\"enumName\":\"POLICY_EFFECTIVE_DATE\",\"orderNo\":2,\"selected\":\"YES\",\"enumFlag\":\"YES\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"保单生效日\"},{\"propertyName\":\"立即生效\",\"propertyCode\":\"immediately\",\"enumValue\":\"POS_IMMEDIATELY_DATE\",\"enumName\":\"POS_IMMEDIATELY_DATE\",\"orderNo\":2,\"selected\":\"YES\",\"enumFlag\":\"YES\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"立即生效\"}]},{\"moduleCode\":\"premiumChangeModule\",\"required\":\"NO\",\"customerType\":null,\"customerSubType\":null,\"schemaDefType\":null,\"posItemModuleProperties\":[{\"propertyName\":\"Premium before and after policy change service\",\"propertyCode\":\"premiumChange\",\"enumValue\":\"premiumChange\",\"enumName\":null,\"orderNo\":1,\"selected\":\"YES\",\"enumFlag\":\"NO\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"Premium before and after policy change service\"}]},{\"moduleCode\":\"signDateModule\",\"required\":\"NO\",\"customerType\":null,\"customerSubType\":null,\"schemaDefType\":null,\"posItemModuleProperties\":[{\"propertyName\":\"POS Application Form Signed Date\",\"propertyCode\":\"signDate\",\"enumValue\":\"signDate\",\"enumName\":null,\"orderNo\":1,\"selected\":\"YES\",\"enumFlag\":\"NO\",\"required\":\"YES\",\"editable\":\"YES\",\"dataType\":null,\"isExtension\":null,\"bizDictKey\":null,\"displayName\":\"POS Application Form Signed Date\"}]},{\"moduleCode\":\"schemaPolicyInsuredAddress\",\"required\":null,\"customerType\":\"PERSON\",\"customerSubType\":\"ADDRESS\",\"schemaDefType\":\"CUSTOMER\",\"posItemModuleProperties\":[{\"propertyName\":\"Address3\",\"propertyCode\":\"address13\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"INPUT\",\"isExtension\":\"NO\",\"bizDictKey\":\"address3\",\"displayName\":\"Address3\"},{\"propertyName\":\"Address Type\",\"propertyCode\":\"addressType\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"SELECT\",\"isExtension\":\"NO\",\"bizDictKey\":\"addressType\",\"displayName\":\"Address Type\"},{\"propertyName\":\"Zip Code\",\"propertyCode\":\"zipCode\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"INPUT\",\"isExtension\":\"NO\",\"bizDictKey\":null,\"displayName\":\"Zip Code\"}]},{\"moduleCode\":\"schemaPolicyInsuredBasic\",\"required\":null,\"customerType\":\"PERSON\",\"customerSubType\":\"BASIC_INFO\",\"schemaDefType\":\"CUSTOMER\",\"posItemModuleProperties\":[{\"propertyName\":\"Date of Birth\",\"propertyCode\":\"birthday\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"DATE\",\"isExtension\":\"NO\",\"bizDictKey\":null,\"displayName\":\"Date of Birth\"},{\"propertyName\":\"Birth Place\",\"propertyCode\":\"birthPlace\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"INPUT\",\"isExtension\":\"NO\",\"bizDictKey\":null,\"displayName\":\"Birth Place\"},{\"propertyName\":\"ID Number\",\"propertyCode\":\"certiNo\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"INPUT\",\"isExtension\":\"NO\",\"bizDictKey\":null,\"displayName\":\"ID Number\"},{\"propertyName\":\"ID Type\",\"propertyCode\":\"certiType\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"SELECT\",\"isExtension\":\"NO\",\"bizDictKey\":\"certiType\",\"displayName\":\"ID Type\"},{\"propertyName\":\"Full Name\",\"propertyCode\":\"fullName\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"INPUT\",\"isExtension\":\"NO\",\"bizDictKey\":null,\"displayName\":\"Full Name\"},{\"propertyName\":\"Gender\",\"propertyCode\":\"gender\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"SELECT\",\"isExtension\":\"NO\",\"bizDictKey\":\"gender\",\"displayName\":\"Gender\"},{\"propertyName\":\"Nationality\",\"propertyCode\":\"nationality\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"SELECT\",\"isExtension\":\"NO\",\"bizDictKey\":\"country\",\"displayName\":\"Nationality\"},{\"propertyName\":\"Relationship with Policyholder\",\"propertyCode\":\"relationshipWithPolicyholder\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"NO\",\"dataType\":\"SELECT\",\"isExtension\":\"NO\",\"bizDictKey\":\"relationship\",\"displayName\":\"Relationship with Policyholder\"}]},{\"moduleCode\":\"schemaPolicyInsuredPhoneInfo\",\"required\":null,\"customerType\":\"PERSON\",\"customerSubType\":\"PHONE\",\"schemaDefType\":\"CUSTOMER\",\"posItemModuleProperties\":[{\"propertyName\":\"Phone No\",\"propertyCode\":\"phoneNo\",\"enumValue\":null,\"enumName\":null,\"orderNo\":null,\"selected\":null,\"enumFlag\":null,\"required\":\"NO\",\"editable\":\"YES\",\"dataType\":\"INPUT\",\"isExtension\":\"NO\",\"bizDictKey\":null,\"displayName\":\"Phone No\"}]}]}]";
        List<PosItemConfigResponse> object = JacksonUtil.toObject(json, new TypeReference<List<PosItemConfigResponse>>() {
        });
        List<PosItemConfigResponse.PosItemModule> addressConfig = object.get(0).getPosItemModules().stream().filter(posItemModule -> CustomerSubTypeEnum.ADDRESS.equals(posItemModule.getCustomerSubType())).collect(Collectors.toList());
        uiModelReflectUtil.convertObjectFromPosConfig(target, addressConfig.get(0).getPosItemModuleProperties(), new JsonMap());
        assertNotNull(target);
    }

    @Test
    public void fetchRoleItemsTest() {
        ApplicationElementItemsResponse applicationElementItemsResponse = new ApplicationElementItemsResponse();
        ApplicationElementsItem applicationElementsItem = new ApplicationElementsItem();
        applicationElementsItem.setCode("phoneType");
        EnumItem enumItem1 = new EnumItem("phone", "phone");
        enumItem1.setEnumItemName("phone");
        applicationElementsItem.setItems(Arrays.asList(enumItem1));

        ApplicationElementsItem applicationElementsItem2 = new ApplicationElementsItem();
        applicationElementsItem2.setCode("addressType");
        EnumItem enumItem = new EnumItem("address", "address");
        enumItem.setEnumItemName("address");
        applicationElementsItem2.setItems(Arrays.asList(enumItem));
        applicationElementItemsResponse.setHolderItems(Arrays.asList(applicationElementsItem, applicationElementsItem2));
        applicationElementItemsResponse.setInsuredItems(Arrays.asList(applicationElementsItem2, applicationElementsItem));

        List<ApplicationElementsItem> roleItems = uiModelReflectUtil.fetchRoleItems(applicationElementItemsResponse, "holderItems");
        Assertions.assertNotNull(roleItems);
    }

}