/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.positems.mock;

import com.zatech.genesis.customer.portal.pos.dataprovider.positems.result.PosItemConfigureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * @Author: weizhen.kong
 */
public class PosMockUtilsTest {

    @InjectMocks
    private PosItemConfigureMockUtils posMockUtils;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testShouldMock() {
        DataProvideContext dataProvideContext = mock(DataProvideContext.class);
        when(dataProvideContext.getParams()).thenReturn(Map.of("mock", true));

        assertTrue(posMockUtils.shouldMock("mock111"));
    }

    @Test
    public void testPosItemConfigureResult() {
        PosItemConfigureResult result = posMockUtils.posItemConfigureResult();
        assertNotNull(result);
        assertNotNull(result.getSchema());
    }
}