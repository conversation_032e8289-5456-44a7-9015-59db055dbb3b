package com.zatech.genesis.customer.portal.pos.dataprovider.steps;

import com.zatech.genesis.customer.portal.biz.common.uimodel.step.StepFilterFactory;
import com.zatech.genesis.customer.portal.pos.dataprovider.steps.result.PosPageStepsResult;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.common.enums.ModuleTypeEnum;

import java.util.Arrays;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class PosPosPageStepsDataProviderTestResult {

    @InjectMocks
    private PosPageStepsDataProvider posPageStepsDataProvider;

    @Mock
    private StepFilterFactory stepFilterFactory;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void provide() {
        Mockito.when(stepFilterFactory.latestSteps(Mockito.any(), Mockito.any())).thenReturn(Arrays.asList());
        OrderContext orderContext = new OrderContext(13233L, "", ModuleTypeEnum.mock);
        DataProvideContext dataProvideContext = new DataProvideContext(orderContext, null);
        orderContext.setOrderNo("no");
        PosPageStepsResult provide = posPageStepsDataProvider.provide(new PosDataEntryTemplate(), dataProvideContext);
        Assertions.assertNotNull(provide);
    }
}