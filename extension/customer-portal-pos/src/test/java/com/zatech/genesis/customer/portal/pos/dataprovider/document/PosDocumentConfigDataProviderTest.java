package com.zatech.genesis.customer.portal.pos.dataprovider.document;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.genesis.customer.portal.pos.dataprovider.document.param.DocumentConfigParam;
import com.zatech.genesis.customer.portal.pos.dataprovider.document.result.DocumentConfigResult;
import com.zatech.genesis.customer.portal.pos.outer.IOuterMetadataService;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPosService;
import com.zatech.genesis.customer.portal.pos.outer.response.PosDocumentConfigResponse;
import com.zatech.genesis.metadata.api.bizdict.response.BizDictTenantResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.common.enums.ModuleTypeEnum;

import java.util.Arrays;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class PosDocumentConfigDataProviderTest {

    @InjectMocks
    private PosDocumentConfigDataProvider posDocumentConfigDataProvider;

    @Mock
    private IOuterPosService outerPosService;

    @Mock
    private IOuterMetadataService outerMetadataService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void provide() {
        PosDocumentConfigResponse posDocumentConfigResponse = new PosDocumentConfigResponse();
        PosDocumentConfigResponse.ConfigDocument configDocument = new PosDocumentConfigResponse.ConfigDocument();
        configDocument.setDocumentType("Card");
        posDocumentConfigResponse.setConfigDocuments(Arrays.asList(configDocument));
        Mockito.when(outerPosService.getConfigDocuments(Mockito.any())).thenReturn(posDocumentConfigResponse);
        DocumentConfigParam configParam = new DocumentConfigParam();
        configParam.setPosItemType(TransTypeEnum.POS_HOLDER_INFO_CHANGES);
        Mockito.when(outerMetadataService.queryBizDictTenant(Mockito.any())).thenReturn(Arrays.asList(new BizDictTenantResponse()));
        OrderContext orderContext = new OrderContext(13233L, "", ModuleTypeEnum.mock);
        DocumentConfigResult provide = posDocumentConfigDataProvider.provide(configParam, new DataProvideContext(orderContext, null));
        Assertions.assertNotNull(provide);
    }
}