package com.zatech.genesis.customer.portal.pos.builder;

import com.fasterxml.jackson.databind.introspect.AnnotatedField;
import com.zatech.genesis.customer.portal.pos.builder.param.PosConfigureEnumBuilderParam;
import com.zatech.genesis.customer.portal.pos.outer.IOuterMetadataService;
import com.zatech.genesis.customer.portal.pos.outer.market.response.ApplicationElementItemsResponse;
import com.zatech.genesis.customer.portal.pos.outer.market.response.ApplicationElementsItem;
import com.zatech.genesis.customer.portal.pos.outer.market.response.EnumItem;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildMetaInfo;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;

import io.swagger.v3.oas.models.media.Schema;
import org.junit.jupiter.api.AfterEach;

import java.util.Arrays;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.APPLICATION_ELEMENT_CONFIG;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;

public class IndividualElementSchemaBuilderTest {

    private AutoCloseable mocks;

    @InjectMocks
    private IndividualElementSchemaBuilder individualElementSchemaBuilder;

    @Mock
    private IOuterMetadataService outerMetadataService;

    @BeforeEach
    public void setUp() {
        mocks = MockitoAnnotations.openMocks(this);
    }

    @Test
    public void buildParam() {
        SchemaNodeBuildContext schemaNodeBuildContext = new SchemaNodeBuildContext();
        SchemaNodeBuildMetaInfo schemaNodeBuildMetaInfo = mock(SchemaNodeBuildMetaInfo.class);
        IndividualElementSchemaBuilder builder = new IndividualElementSchemaBuilder(outerMetadataService);

        // Define your test data
        JsonMap jsonMap = new JsonMap();
        jsonMap.put("dictKey", "addressType");
        schemaNodeBuildContext.setBuilderParams(jsonMap);
        ApplicationElementItemsResponse applicationElementItemsResponse = new ApplicationElementItemsResponse();
        ApplicationElementsItem applicationElementsItem = new ApplicationElementsItem();
        applicationElementsItem.setCode("phoneType");
        EnumItem enumItem1 = new EnumItem("phone", "phone");
        enumItem1.setEnumItemName("phone");
        applicationElementsItem.setItems(Arrays.asList(enumItem1));

        ApplicationElementsItem applicationElementsItem2 = new ApplicationElementsItem();
        applicationElementsItem2.setCode("addressType");
        EnumItem enumItem = new EnumItem("address", "address");
        enumItem.setEnumItemName("address");
        applicationElementsItem2.setItems(Arrays.asList(enumItem));
        applicationElementItemsResponse.setHolderItems(Arrays.asList(applicationElementsItem, applicationElementsItem2));
        applicationElementItemsResponse.setInsuredItems(Arrays.asList(applicationElementsItem2, applicationElementsItem));
        JsonMap jsonMap2 = new JsonMap();
        jsonMap2.put(APPLICATION_ELEMENT_CONFIG, Arrays.asList(applicationElementsItem2));
        schemaNodeBuildContext.setTemplateParams(jsonMap2);

        SchemaNodeBuildContext.FieldMeta fieldMeta = new SchemaNodeBuildContext.FieldMeta();
        fieldMeta.setField(mock(AnnotatedField.class));
        schemaNodeBuildContext.setFieldMeta(fieldMeta);
        PosConfigureEnumBuilderParam result = builder.buildParam(schemaNodeBuildContext, schemaNodeBuildMetaInfo);

        // Add assertions to validate the result
        assertNotNull(result);
    }

    @Test
    public void buildParam2() {
        SchemaNodeBuildContext schemaNodeBuildContext = new SchemaNodeBuildContext();
        SchemaNodeBuildMetaInfo schemaNodeBuildMetaInfo = mock(SchemaNodeBuildMetaInfo.class);
        IndividualElementSchemaBuilder builder = new IndividualElementSchemaBuilder(outerMetadataService);

        // Define your test data
        JsonMap jsonMap = new JsonMap();
        schemaNodeBuildContext.setBuilderParams(jsonMap);
        ApplicationElementItemsResponse applicationElementItemsResponse = new ApplicationElementItemsResponse();
        ApplicationElementsItem applicationElementsItem = new ApplicationElementsItem();
        applicationElementsItem.setCode("phoneType");
        EnumItem enumItem1 = new EnumItem("phone", "phone");
        enumItem1.setEnumItemName("phone");
        applicationElementsItem.setItems(Arrays.asList(enumItem1));

        ApplicationElementsItem applicationElementsItem2 = new ApplicationElementsItem();
        applicationElementsItem2.setCode("addressType");
        EnumItem enumItem = new EnumItem("address", "address");
        enumItem.setEnumItemName("address");
        applicationElementsItem2.setItems(Arrays.asList(enumItem));
        applicationElementItemsResponse.setHolderItems(Arrays.asList(applicationElementsItem, applicationElementsItem2));
        applicationElementItemsResponse.setInsuredItems(Arrays.asList(applicationElementsItem2, applicationElementsItem));
        JsonMap jsonMap2 = new JsonMap();
        jsonMap2.put(APPLICATION_ELEMENT_CONFIG, Arrays.asList(applicationElementsItem2));
        schemaNodeBuildContext.setTemplateParams(jsonMap2);

        SchemaNodeBuildContext.FieldMeta fieldMeta = new SchemaNodeBuildContext.FieldMeta();
        fieldMeta.setField(mock(AnnotatedField.class));
        schemaNodeBuildContext.setFieldMeta(fieldMeta);
        PosConfigureEnumBuilderParam result = builder.buildParam(schemaNodeBuildContext, schemaNodeBuildMetaInfo);

        // Add assertions to validate the result
        assertNotNull(result);
    }

    @Test
    public void buildSchema() {
        PosConfigureEnumBuilderParam builderParam = new PosConfigureEnumBuilderParam();
        builderParam.setFileCode("code");
        Schema schema = individualElementSchemaBuilder.buildSchema(mock(Schema.class), builderParam, mock(SchemaNodeBuildContext.class), mock(SchemaNodeBuildMetaInfo.class));
        Assertions.assertNotNull(schema);
    }

    @AfterEach
    void tearDown() throws Exception {
        mocks.close();
    }
}