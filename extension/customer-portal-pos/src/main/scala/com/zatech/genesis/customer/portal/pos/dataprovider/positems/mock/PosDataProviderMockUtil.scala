package com.zatech.genesis.customer.portal.pos.dataprovider.positems.mock

import com.zatech.genesis.customer.portal.pos.dataprovider.positems.result.PosItemConfigureResult
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate
import com.zatech.genesis.portal.toolbox.jsonschema.node.SchemaNodeFactory
import com.zatech.genesis.portal.toolbox.jsonschema.wrapper.withclass.SchemaWithClassWrapperFactory.schemaNode2SchemaWithClass
import com.zatech.genesis.portal.toolbox.jsonschema.wrapper.withclass.context.SchemaWithClassWrapperContext
import com.zatech.genesis.portal.toolbox.util.jsonschema.SwaggerJsonSchemaHelper

class PosDataProviderMockUtil {

  def posItemConfigureResult(): PosItemConfigureResult = {
    val node = SchemaNodeFactory.newResolvedSchemaNodeByClass(classOf[PosDataEntryTemplate])
    val context = SchemaWithClassWrapperContext.create(classOf[PosDataEntryTemplate])
    val wrapper = node.wrap(context)
    new PosItemConfigureResult(SwaggerJsonSchemaHelper.fromJsonSchemaToJsonMap(wrapper.schema))
  }

}
