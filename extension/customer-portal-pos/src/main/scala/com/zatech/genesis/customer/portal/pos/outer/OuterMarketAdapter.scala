/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
package com.zatech.genesis.customer.portal.pos.outer

import com.za.cqrs.util.Functions.{doIf, throwIf}
import com.zatech.gaia.resource.components.enums.common.YesNoEnum
import com.zatech.genesis.customer.portal.pos.outer.market.response.ApplicationElementItemsResponse
import com.zatech.genesis.customer.portal.pos.outer.market.MarketErrorCodes
import com.zatech.genesis.customer.portal.pos.outer.market.outer.OuterMarketService
import com.zatech.genesis.customer.portal.pos.outer.market.response.{ApplicationElementItemsResponse, ApplicationElementsItem, EnumItem}
import com.zatech.genesis.market.api.structure.request.{PackageApplicationElementsEnumRequest, QueryGoodsRelatingRequest, QueryPackageRelatingRequest}
import com.zatech.genesis.market.api.structure.response.{ChildEnumItemResponse, GoodsRelatingResponse, PackageApplicationElementsItemResponse}
import com.zatech.genesis.portal.toolbox.exception.CommonException
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import com.zatech.genesis.portal.toolbox.share.Loggable
import com.zatech.octopus.core.util.JacksonUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util
import java.util.{Objects, Optional}

/**
 * <AUTHOR>
 * @create 2024/1/24 10:50
 * */
@Component
class OuterMarketAdapter @Autowired()(
                                       outerMarketService: OuterMarketService
                                     ) extends Loggable {

  def queryGoodsDetailInfo(goodsId: Long): GoodsRelatingResponse = {
    def buildRequest: QueryGoodsRelatingRequest = {
      val request = new QueryGoodsRelatingRequest
      request.setGoodsId(goodsId)
      request.setQuerySalesAttributes(true)
      request.setQueryCoveragePlan(true)
      request.setQueryBasic(true)
      request.setQueryDocumentsDisplay(true)
      val req = new QueryPackageRelatingRequest
      req.setQueryPackageAgreement(true)
      req.setQueryBasic(true)
      req.setQueryApplicationElements(true)
      req.setQueryProductAndLiability(true)
      req.setQueryPackageBenefitConfiguration(true)
      req.setQueryDeclarationConfiguration(true)
      req.setQueryUwQuestionnaireConfiguration(true)
      request.setQueryPackageRelating(req)
      request
    }

    val goodsInfo = logTime("query market goods info") {
      val request = buildRequest
      outerMarketService.queryGoodsRelating(request)
    }
    throwIf(goodsInfo == null, () => CommonException.byErrorAndParams(MarketErrorCodes.goods_not_found, goodsId))
    goodsInfo
  }

  def queryApplicationElements(goodsId: Long, planId: Long): ApplicationElementItemsResponse = {
    def buildRequest: QueryGoodsRelatingRequest = {
      val request = new QueryGoodsRelatingRequest
      request.setGoodsId(goodsId)
      request.setQueryCoveragePlan(true)
      request.setQueryBasic(true)
      request
    }

    val goodsInfo = {
      val request = buildRequest
      outerMarketService.queryGoodsRelating(request)
    }
    throwIf(goodsInfo == null, () => CommonException.byErrorAndParams(MarketErrorCodes.goods_not_found, goodsId))

    def buildApplicationElementRequest: PackageApplicationElementsEnumRequest = {
      if (Objects.isNull(goodsInfo.getCoveragePlans)) {
        return new PackageApplicationElementsEnumRequest
      }
      val plan = goodsInfo.getCoveragePlans.stream().filter(e => Objects.equals(planId, e.getPlanId)).findFirst().get()
      throwIf(plan == null || plan.getPackageId == null, () => CommonException.byErrorAndParams(MarketErrorCodes.package_not_found, goodsId))

      val request = new PackageApplicationElementsEnumRequest
      request.setPackageId(plan.getPackageId)
      request.setNeedPayerItems(true)
      request.setNeedPolicyItems(true)
      request.setNeedObjectItems(true)
      request.setNeedSecondaryLifeInsuredItems(true)
      request.setNeedNomineeItems(true)
      request.setNeedConsenteeItems(true)
      request.setNeedBeneficiaryItems(true)
      request.setNeedBeneficialOwnerItems(true)
      request.setNeedHolderItems(true)
      request.setNeedInsuredItems(true)
      request.setNeedTrusteeItems(true)
      request.setNeedPremiumFunderItems(true)
      request.setNeedAssigneeItems(true)
      request
    }

    val goodsApplicationElementInfo = {
      val request = buildApplicationElementRequest
      doIf(log.isDebugEnabled, () => log.debug("Query market request: {}", JacksonUtil.toJSONString(request)))
      outerMarketService.queryPackageApplicationElementEnumItem(request)
    }
    throwIf(goodsApplicationElementInfo == null, () => CommonException.byErrorAndParams(MarketErrorCodes.package_not_found, goodsId))
    doIf(log.isDebugEnabled, () => log.debug("Query market result: {}", JacksonUtil.toJSONString(goodsApplicationElementInfo)))

    val applicationElementItemsResponse = new ApplicationElementItemsResponse
    applicationElementItemsResponse.setPackageId(goodsApplicationElementInfo.getPackageId)
    applicationElementItemsResponse.setSecondaryLifeInsuredItems(convertElementItems(goodsApplicationElementInfo.getSecondaryLifeInsuredItem))
    applicationElementItemsResponse.setPremiumFunderItems(convertElementItems(goodsApplicationElementInfo.getPremiumFunderItems))
    applicationElementItemsResponse.setTrusteeItems(convertElementItems(goodsApplicationElementInfo.getTrusteeItems))
    applicationElementItemsResponse.setObjectItems(convertElementItems(goodsApplicationElementInfo.getObjectItems))
    applicationElementItemsResponse.setHolderItems(convertElementItems(goodsApplicationElementInfo.getHolderItems))
    applicationElementItemsResponse.setInsuredItems(convertElementItems(goodsApplicationElementInfo.getInsuredItems))
    applicationElementItemsResponse.setPayerItems(convertElementItems(goodsApplicationElementInfo.getPayerItems))
    applicationElementItemsResponse.setPolicyItems(convertElementItems(goodsApplicationElementInfo.getPolicyItems))
    applicationElementItemsResponse.setNomineeItems(convertElementItems(goodsApplicationElementInfo.getNomineeItems))
    applicationElementItemsResponse.setBeneficiaryItems(convertElementItems(goodsApplicationElementInfo.getBeneficiaryItems))
    applicationElementItemsResponse.setBeneficialOwnerItems(convertElementItems(goodsApplicationElementInfo.getBeneficialOwnerItems))
    applicationElementItemsResponse.setConsenteeItems(convertElementItems(goodsApplicationElementInfo.getConsenteeItems))
    applicationElementItemsResponse.setAssigneeItems(convertElementItems(goodsApplicationElementInfo.getAssigneeItems))
    applicationElementItemsResponse
  }

  def convertElementItems(items: Seq[PackageApplicationElementsItemResponse]): Seq[ApplicationElementsItem] = {
    def convertEnumItem(child: util.ArrayList[EnumItem], childItem: ChildEnumItemResponse) = {
      val enumItem = new EnumItem(childItem.getSchemaFieldCode, null, null, null)
      enumItem.setChildren(covertItemChild(Option(childItem)))
      child.add(enumItem)
    }

    items
      .filter(f => f.getItems != null && !f.getItems.isEmpty)
      .map { i =>
        val item = new ApplicationElementsItem
        item.setCode(i.getSchemaFieldCode)
        Option.apply(i.getIsUpdatableWhenPos).ifPresent(e => item.setIsUpdatableWhenPos(YesNoEnum.getEnumCode(e)))
        Option.apply(i.getIsUpdatableWhenUw).ifPresent(e => item.setIsUpdatableWhenUw(YesNoEnum.getEnumCode(e)))
        val enumItems = i.getItems.map { item =>
          val enumItem = new EnumItem(item.getValue, item.getDisplayName, item.getEnumItemName, item.getBizDictKey)
          enumItem.setExtension1(item.getItemExtend1)
          enumItem.setExtension2(item.getItemExtend2)
          enumItem.setExtension3(item.getItemExtend3)
          enumItem.setExtension4(item.getItemExtend4)
          enumItem.setExtension5(item.getItemExtend5)

          val child = new util.ArrayList[EnumItem]()
          Optional.ofNullable(item.getChildren)
            .ifPresentOrElse(childItems => childItems.foreach(childItem => convertEnumItem(child, childItem)),
              () => Option(item.getChild).map(c => convertEnumItem(child, c)))
          enumItem.setChildren(child)
          enumItem
        }
        item.setItems(enumItems)
        item
      }
  }

  def covertItemChild(child: Option[ChildEnumItemResponse]): Seq[EnumItem] = {
    child match {
      case Some(c) if c.getItems != null =>
        c.getItems.flatMap { e =>
          val dictKey = Option(e.getExtensions)
            .flatMap(ex => ex.stream().findFirst())
            .flatMap(ci => ci.getItems.stream().findFirst())
            .map(_.getBizDictKey)
            .getOrElse(e.getBizDictKey)
          val enumItem = new EnumItem(e.getValue, e.getDisplayName, e.getEnumItemName, dictKey)
          enumItem.setChildren(covertItemChild(Option(e.getChild)))
          Seq(enumItem)
        }
      case _ => Seq.empty
    }
  }

}
