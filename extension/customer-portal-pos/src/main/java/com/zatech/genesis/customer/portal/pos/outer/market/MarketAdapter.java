/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.market;

import com.zatech.genesis.customer.portal.pos.outer.market.outer.OuterMarketService;
import com.zatech.genesis.customer.portal.pos.outer.market.response.GoodsBasicInfoResp;
import com.zatech.genesis.market.api.structure.base.GoodsCoveragePlanBase;
import com.zatech.genesis.market.api.structure.base.I18nMessageBase;
import com.zatech.genesis.market.api.structure.request.QueryGoodsRelatingListRequest;
import com.zatech.genesis.market.api.structure.request.QueryGoodsRelatingRequest;
import com.zatech.genesis.market.api.structure.response.GoodsCoveragePlanResponse;
import com.zatech.genesis.market.api.structure.response.GoodsRelatingResponse;
import com.zatech.octopus.component.sleuth.TraceOp;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("fm-marketAdapter")
public class MarketAdapter {

    @Autowired
    private OuterMarketService outerMarketService;

    public List<GoodsBasicInfoResp> queryGoodsBasicList(String... goodsCodes) {
        if (ArrayUtils.isEmpty(goodsCodes)) {
            return Collections.emptyList();
        }
        var queryGoodsRelatingListRequest = new QueryGoodsRelatingListRequest();
        queryGoodsRelatingListRequest.setGoodsCodeList(List.of(goodsCodes));
        return outerMarketService.queryGoodsRelatingList(queryGoodsRelatingListRequest);
    }

    public Optional<GoodsBasicInfoResp> queryGoodsBasicOpt(String goodsCodes) {
        return queryGoodsBasicList(goodsCodes).stream().findFirst();
    }

    public Optional<GoodsBasicInfoResp> queryGoodsBasicOpt(Long goodsId) {
        return queryGoodsBasicList(goodsId).stream().findFirst();
    }

    public List<GoodsBasicInfoResp> queryGoodsBasicList(Long... goodsIds) {
        if (ArrayUtils.isEmpty(goodsIds)) {
            return Collections.emptyList();
        }
        var queryGoodsRelatingListRequest = new QueryGoodsRelatingListRequest();
        queryGoodsRelatingListRequest.setGoodsIdList(List.of(goodsIds));
        return outerMarketService.queryGoodsRelatingList(queryGoodsRelatingListRequest);
    }

    public GoodsRelatingResponse queryGoodsRelating(QueryGoodsRelatingRequest queryGoodsRelatingRequest) {
        return outerMarketService.queryGoodsRelating(queryGoodsRelatingRequest);
    }

    public String queryGoodsCode(Long goodsId) {
        return queryGoodsBasicOpt(goodsId).map(GoodsBasicInfoResp::getGoodsCode).orElse(null);
    }

    public String queryPlanName(Long goodsId, Long planId) {
        QueryGoodsRelatingRequest queryGoodsRelatingRequest = new QueryGoodsRelatingRequest();
        queryGoodsRelatingRequest.setGoodsId(goodsId);
        queryGoodsRelatingRequest.setPlanId(planId);
        queryGoodsRelatingRequest.setQueryCoveragePlan(true);
        GoodsRelatingResponse goodsRelatingResponse = queryGoodsRelating(queryGoodsRelatingRequest);
        return Optional.ofNullable(goodsRelatingResponse)
            .map(x -> x.getCoveragePlans().stream().findFirst().orElse(new GoodsCoveragePlanResponse()))
            .map(GoodsCoveragePlanBase::getI18nPlanName)
            .flatMap(list -> list.stream()
            .filter(e -> Objects.equals(e.getLang(), TraceOp.getLanguage()))
            .findFirst()
            .map(I18nMessageBase::getI18nContent))
            .orElseGet(() -> Optional.ofNullable(goodsRelatingResponse)
                .map(x -> x.getCoveragePlans().stream().findFirst().orElse(new GoodsCoveragePlanResponse())).map(GoodsCoveragePlanBase::getGoodsPlanName).orElse(null));
    }

}
