/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.payment;

import com.zatech.genesis.customer.portal.pos.dataprovider.payment.request.PaymentResultParam;
import com.zatech.genesis.customer.portal.pos.dataprovider.payment.result.PaymentResult;
import com.zatech.genesis.customer.portal.pos.event.PayOrderNoLoadEvent;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPaymentService;
import com.zatech.genesis.payment.gateway.api.response.PayOrderResp;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProviderKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.UIModelDataProvider;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;

import lombok.AllArgsConstructor;

import org.springframework.context.ApplicationContext;

/**
 * @Author: weizhen.kong
 */
@UIModelDataProvider(
        name = "paymentResultQuery",
        productCategories = {FreeMartProductCategoryEnum.ALL},
        kind = DataProviderKind.data,
        desc = "查询是否支付成功")
@AllArgsConstructor
public class QueryPaymentResultDataProvider implements IDataProvider<PaymentResultParam, PaymentResult> {

    private final ApplicationContext applicationContext;

    private final IOuterPaymentService outerPaymentService;

    @Override
    public PaymentResult provide(PaymentResultParam unused, DataProvideContext dataProvideContext) {
        PayOrderNoLoadEvent payOrderNoLoadEvent = new PayOrderNoLoadEvent(dataProvideContext.getOrder().getOrderNo());
        applicationContext.publishEvent(payOrderNoLoadEvent);
        PayOrderResp payOrderResp = outerPaymentService.queryPayOrder(payOrderNoLoadEvent.getPayOrderNo());
        return PaymentResult.from(payOrderResp);
    }

}