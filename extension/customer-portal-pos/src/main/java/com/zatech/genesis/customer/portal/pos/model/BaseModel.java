/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.model;

import com.zatech.genesis.customer.portal.pos.model.jsonschema.ExtensionSchemaBuilder;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.UIModelSystemProperty;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuilder;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

/**
 * This is a BaseModel support dynamic fields
 */
@Getter
@Setter
public abstract class BaseModel implements Serializable {

    @Schema(description = "扩展字段")
    @UIModelSystemProperty
    @SchemaNodeBuilder(ExtensionSchemaBuilder.class)
    private Map<String, Object> extensions;

    public BaseModel addExtension(String key, Object value) {
        if (this.extensions == null) {
            this.extensions = new HashMap<>();
        }
        this.extensions.put(key, value);
        return this;
    }

}
