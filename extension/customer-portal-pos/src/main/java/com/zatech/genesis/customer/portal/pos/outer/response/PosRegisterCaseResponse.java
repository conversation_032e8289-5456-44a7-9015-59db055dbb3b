/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.response;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
public class PosRegisterCaseResponse {

    /**
     * 保全申请编号
     */
    @Schema(title = "The number of POS case")
    private String caseNo;

    /**
     * 保单号
     */
    @Schema(title = "The number of policy")
    private String policyNo;

    /**
     * 业务申请号
     */
    @Schema(title = "The  number of business system")
    private String bizApplyNo;

    /**
     * 第三方保全申请编号
     */
    @Schema(title = "The number of external case")
    private String externalCaseNo;

    /**
     * 第三方序列号
     */
    @Schema(title = "The number of external case")
    private String externalSerialNo;

    /**
     * 第三方交易号
     */
    @Schema(title = "The number of external transaction")
    private String externalTransactionNo;

    /**
     * rule engine 校验结果
     */
    @Schema(title = "The information of rule decision")
    private PosValidateCaseResponse.PosRuleDecisionResponse posRuleDecision;

}