/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.payment.result;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayStatusEnum;
import com.zatech.genesis.customer.portal.pos.common.BaseResult;
import com.zatech.genesis.payment.gateway.api.response.PayOrderResp;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import org.springframework.util.Assert;

import static com.zatech.genesis.customer.portal.pos.dataprovider.payment.result.TransStatusEnum.FAILURE;
import static com.zatech.genesis.customer.portal.pos.dataprovider.payment.result.TransStatusEnum.PAYING;
import static com.zatech.genesis.customer.portal.pos.dataprovider.payment.result.TransStatusEnum.SUCCESS;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
@Accessors(chain = true)
public class PaymentResult extends BaseResult {

    private TransStatusEnum transStatus;

    private String amount;

    private String currency;

    /**
     * 错误信息
     */
    private String errMsg;

    private String errCode;

    /**
     * 第三方交易号
     */
    private String thirdPartyTransNo;

    /**
     * 第三方订单
     */
    private String thirdPartyOrderNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 重定向URL
     */
    private String redirectUrl;

    private Date payDate;

    public static PaymentResult from(PayOrderResp payOrderResp) {
        PaymentResult transaction = new PaymentResult()
                .setAmount(payOrderResp.getAmount())
                .setCurrency(payOrderResp.getCurrency().getCountry())
                .setPayDate(payOrderResp.getPayDate() == null ? new Date() : payOrderResp.getPayDate())
                .setRedirectUrl(payOrderResp.getRedirectUrl())
                .setThirdPartyOrderNo(payOrderResp.getPayOrderNo())
                .setThirdPartyTransNo(payOrderResp.getThirdPartyNo())
                .setErrMsg(payOrderResp.getErrorMsg())
                .setErrCode(payOrderResp.getErrorCode());
        transaction.status(payOrderResp.getExceptionFlag() == YesNoEnum.YES, payOrderResp.getPayStatus());
        return transaction;
    }

    public void status(boolean unknown, PayStatusEnum value) {
        if (unknown) {
            this.setTransStatus(TransStatusEnum.UNKNOWN);
        } else {
            this.setTransStatus(convertPayStatusEnumToTransStatusEnum(value));
        }
    }

    private TransStatusEnum convertPayStatusEnumToTransStatusEnum(PayStatusEnum value) {
        Assert.notNull(value, "invalid pay status.");
        switch (value) {
            case SUCCESS:
                return SUCCESS;
            case FAILED:
            case CANCEL:
                return FAILURE;
            default:
                return PAYING;
        }
    }
}