/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.util;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.BaseModel;
import com.zatech.genesis.customer.portal.pos.outer.market.response.ApplicationElementItemsResponse;
import com.zatech.genesis.customer.portal.pos.outer.market.response.ApplicationElementsItem;
import com.zatech.genesis.customer.portal.pos.outer.request.PosCaseBaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.request.PosRegisterCaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.PosItemConfigResponse.PosItemModuleProperty;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;
import com.zatech.genesis.customer.portal.pos.common.CommonConstant;
import com.zatech.genesis.customer.portal.pos.mapper.PosCaseMapper;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.ObjectUtils;

/**
 * @Author: weizhen.kong
 */
@Slf4j
public class UiModelReflectUtil {

    private UiModelReflectUtil() {

    }

    public static void convertObjectFromPosConfig(Object target, List<PosItemModuleProperty> posConfigure, JsonMap jsonMap) {
        Class current = target.getClass();
        while (!current.equals(Object.class) && !current.equals(BaseModel.class)) {
            Class finalCurrent = current;
            posConfigure.stream().forEach(config -> setDefaults(target, finalCurrent, config, jsonMap));
            current = current.getSuperclass();
        }
    }

    public static JsonMap convertOriginalUiModel(BaseModel model) {
        JsonMap jsonMap = new JsonMap();
        for (java.lang.reflect.Field field : model.getClass().getDeclaredFields()) {
            try {
                field.setAccessible(true);
                if (ObjectUtils.isNotEmpty(field.get(model))) {
                    jsonMap.put(field.getName(), field.get(model));
                }
            } catch (Exception exception) {
                log.warn("Build original json map error, file name: {}", field.getName());
            }
        }
        return jsonMap;
    }

    public static List<ApplicationElementsItem> fetchRoleItems(ApplicationElementItemsResponse applicationElementItems, String roleItem) {
        if (applicationElementItems == null) {
            log.warn("Application element item empty for roleItem: {}", roleItem);
            return Collections.emptyList();
        }
        try {
            Field declaredField = applicationElementItems.getClass().getDeclaredField(roleItem);
            declaredField.setAccessible(true);
            return (List<ApplicationElementsItem>) declaredField.get(applicationElementItems);
        } catch (Exception e) {
            log.error("Fetch application elements item error, roleItem: {}", roleItem, e);
        }
        return Collections.emptyList();
    }

    public static void setDefaults(Object instance, Class current, PosItemModuleProperty config, JsonMap jsonMap) {
        if (instance == null) {
            return;
        }

        String fieldName = config.getPropertyCode();
        try {
            Field field = current.getDeclaredField(fieldName);
            jsonMap.put(fieldName + CommonConstant.EDITABLE_SUFFIX, YesNoEnum.YES.equals(config.getEditable()));
            field.setAccessible(true);

            if (field.getType() == String.class) {
                field.set(instance, "defaultString");
            } else if (field.getType() == Integer.class) {
                field.set(instance, 0);
            } else if (field.getType() == Date.class) {
                field.set(instance, new Date());
            } else if (field.getType().isEnum()) {
                Object[] enumConstants = field.getType().getEnumConstants();
                if (enumConstants.length > 0) {
                    field.set(instance, enumConstants[0]);
                }
            } else if (field.getType() == Long.class) {
                field.set(instance, 0L);
            } else if (field.getType() == BigDecimal.class) {
                field.set(instance, new BigDecimal(110));
            } else if (field.getType() == LocalDate.class) {
                field.set(instance, LocalDate.now());
            } else {
                log.warn("Not support field type: {}, for instance: {}", field.getType(), instance);
            }
        } catch (Exception exception) {
            log.warn("Config field not found: {}, class: {}, msg: {}", fieldName, current.getName(), exception.getMessage());
        }

    }

    public static FlowStrategy convertUimodel2PosCase(PosDataEntryTemplate param, PosRegisterCaseRequest businessModel) {
        Optional.ofNullable(param.getPayResult()).ifPresent(payResult -> {
            businessModel.setExternalTransactionNo(payResult.getThirdPartyNo());
            businessModel.setExternalSerialNo(payResult.getThirdPartyNo2());
            businessModel.setPayOrderNo(payResult.getPayOrderNo());
        });
        businessModel.setPosRegisterSource("CHANNEL_API");
        businessModel.setPolicyNo(param.getPolicyNo());

        PosCaseBaseRequest.PosTransaction posTransaction = buildPosTransaction(param.getPosApplicationInfo(), param.getPosItemType());

        LinkedList<PosCaseBaseRequest.PosTransaction> posTransactions = new LinkedList<>();
        posTransactions.add(posTransaction);
        businessModel.setTransactionList(posTransactions);
        businessModel.setAttachmentInfoList(PosCaseMapper.MAPPER.convertAttachments(param.getAttachmentInfo()));

        if (ObjectUtils.allNull(param.getPolicyHolder(), param.getPolicyInsured())) {
            return FlowStrategy.Continue;
        }
        //目前只处理单个被保人
        PosCaseBaseRequest.PolicyChangeInfoAfter policyChangeInfoAfter = new PosCaseBaseRequest.PolicyChangeInfoAfter();
        posTransaction.setPolicyChangeInfoAfter(policyChangeInfoAfter);
        policyChangeInfoAfter.setPolicyHolder(PosCaseMapper.MAPPER.convertHolder(param.getPolicyHolder()));
        if (param.getPolicyHolder() != null && param.getPolicyHolder().getEmail() != null) {
            policyChangeInfoAfter.getPolicyHolder().setEmails(Arrays.asList(param.getPolicyHolder().getEmail()));
        }
        if (TransTypeEnum.POS_INSURED_INFO_CHANGES.equals(param.getPosItemType()) && ObjectUtils.allNotNull(param.getPolicyInsured())) {
            PosCaseBaseRequest.PolicyInsurant policyInsurant = PosCaseMapper.MAPPER.convertInsured(param.getPolicyInsured());
            if (param.getPolicyInsured() != null && param.getPolicyInsured().getEmail() != null) {
                policyInsurant.setEmails(Arrays.asList(param.getPolicyInsured().getEmail()));
            }
            policyInsurant.setPolicyInsurantId(param.getPolicyInsured().getPolicyInsurantId());
            policyChangeInfoAfter.setPolicyInsurantList(Arrays.asList(policyInsurant));
        }

        //如果客户选择了同步修改被保人信息，默认增加一个POS_INSURED_INFO_CHANGES的保全项
        if (TransTypeEnum.POS_HOLDER_INFO_CHANGES.equals(param.getPosItemType()) && Boolean.TRUE.equals(param.getPolicyHolder().getCascadeUpdateInsured())) {
            PosCaseBaseRequest.PosTransaction posInsuredTransaction = buildPosTransaction(param.getPosApplicationInfo(), TransTypeEnum.POS_INSURED_INFO_CHANGES);
            businessModel.getTransactionList().add(posInsuredTransaction);
            posInsuredTransaction.setPolicyChangeInfoAfter(new PosCaseBaseRequest.PolicyChangeInfoAfter());
            //todo 临时处理一下
            PosDataEntryTemplate.PolicyInsured policyInsured = param.getPolicyInsured();
            if (param.getPolicyInsured() == null) {
                policyInsured = new PosDataEntryTemplate.PolicyInsured();
                policyInsured.setCustomerId(param.getInsurantCustomerId());
                policyInsured.setPolicyInsurantId(param.getPolicyInsurantId());
            }
            PosCaseBaseRequest.PolicyInsurant policyInsurant = PosCaseMapper.MAPPER.convertInsured(policyInsured);
            if (param.getPolicyInsured() != null && param.getPolicyInsured().getEmail() != null) {
                policyInsurant.setEmails(Arrays.asList(param.getPolicyInsured().getEmail()));
            }

            // todo policyInsurant赋值customerId,policyInsurantId
            posInsuredTransaction.getPolicyChangeInfoAfter().setPolicyInsurantList(Arrays.asList(policyInsurant));
        }
        return FlowStrategy.Continue;
    }

    private static PosCaseBaseRequest.PosTransaction buildPosTransaction(PosDataEntryTemplate.PosApplicationInfo posApplicationInfo, TransTypeEnum transTypeEnum) {
        PosCaseBaseRequest.PosTransaction posTransaction = PosCaseMapper.MAPPER.convertPosApplication(posApplicationInfo, transTypeEnum);
        if (ObjectUtils.anyNull(posTransaction)) {
            posTransaction = new PosCaseBaseRequest.PosTransaction();
        }
        return posTransaction;
    }

}