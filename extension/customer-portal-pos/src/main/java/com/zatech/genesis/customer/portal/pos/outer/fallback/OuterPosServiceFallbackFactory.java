/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.fallback;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPosService;
import com.zatech.genesis.customer.portal.pos.outer.request.PosRegisterCaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.PolicyPosItemResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosCaseCalculationResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosDocumentConfigResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosItemConfigResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosRegisterCaseResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosValidateCaseResponse;
import com.zatech.genesis.customer.portal.pos.exception.PosPortalErrorCode;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * @Author: weizhen.kong
 */
@Slf4j
@Component
public class OuterPosServiceFallbackFactory extends AbstractFallbackFactory<IOuterPosService> {

    @Override
    public IOuterPosService create(Throwable cause) {
        return new IOuterPosService() {

            @Override
            public List<PolicyPosItemResponse> listPosItems(String policyNo) {
                throw outerServiceException(cause, PosPortalErrorCode.POS_SERVICE_UNAVAILABLE, String.format("policyNo: %s", policyNo));
            }

            @Override
            public List<PosItemConfigResponse> listConfigurations(List<TransTypeEnum> transType, Long goodsId, Long planId) {
                throw outerServiceException(cause, PosPortalErrorCode.POS_SERVICE_UNAVAILABLE, String.format("transType: %s, goodsId: %s, planId: %s", transType, goodsId, planId));
            }

            @Override
            public PosValidateCaseResponse validateCase(PosRegisterCaseRequest validationRequest) {
                throw outerServiceException(cause, PosPortalErrorCode.POS_SERVICE_UNAVAILABLE);
            }

            @Override
            public PosRegisterCaseResponse applyCase(PosRegisterCaseRequest posCaseRequest) {
                throw outerServiceException(cause, PosPortalErrorCode.POS_SERVICE_UNAVAILABLE, String.format("policyNo: %s, transType: %s", posCaseRequest.getPolicyNo(), posCaseRequest.getTransactionList().get(0).getPosTransType()));
            }

            @Override
            public PosDocumentConfigResponse getConfigDocuments(List<String> itemCodes) {
                throw outerServiceException(cause, PosPortalErrorCode.POS_SERVICE_UNAVAILABLE);
            }

            @Override
            public PosCaseCalculationResponse calculateCase(PosRegisterCaseRequest calculationRequest) {
                throw outerServiceException(cause, PosPortalErrorCode.POS_SERVICE_UNAVAILABLE);
            }

        };
    }

    @Override
    public String outerServiceKey() {
        return "pos";
    }
}