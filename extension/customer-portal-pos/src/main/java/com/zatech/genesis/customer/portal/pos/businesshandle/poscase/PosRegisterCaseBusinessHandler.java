/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.businesshandle.poscase;

import com.zatech.gaia.resource.components.enums.bcp.PayMethodEnum;
import com.zatech.gaia.resource.components.enums.customer.PolicyCustomerEnum;
import com.zatech.gaia.resource.graphene.customer.AccountTypeEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.event.PosRegisterBeforeEvent;
import com.zatech.genesis.customer.portal.pos.mapper.PosCaseMapper;
import com.zatech.genesis.customer.portal.pos.outer.IOuterCustomerCenterService;
import com.zatech.genesis.customer.portal.pos.outer.IOuterCustomerService;
import com.zatech.genesis.customer.portal.pos.outer.IOuterMetadataService;
import com.zatech.genesis.customer.portal.pos.outer.request.PartyCustomerCreateRequest;
import com.zatech.genesis.customer.portal.pos.outer.request.PosRegisterCaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.CustomerCenterIndividualResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PartyCustomerCreateResponse;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PosPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zhongan.graphene.pos.online.api.common.pos.PosCaseCollectionAccountInfo;
import com.zhongan.graphene.pos.online.api.common.pos.PosPayerInfo;

import java.util.Arrays;

import lombok.AllArgsConstructor;

import org.springframework.context.ApplicationContext;

import static com.zatech.genesis.customer.portal.pos.exception.PosPortalErrorCode.POS_SERVICE_UNAVAILABLE;
import static com.zatech.genesis.customer.portal.pos.util.UiModelReflectUtil.convertUimodel2PosCase;

/**
 * @Author: weizhen.kong
 */
@AllArgsConstructor
@UIModelBusinessHandler(name = "posRegisterCase", kind = BusinessHandlerKind.pos, desc = "保全申请")
public class PosRegisterCaseBusinessHandler implements IBusinessHandler<PosPhase, PosDataEntryTemplate, PosRegisterCaseRequest> {

    private final ApplicationContext applicationContext;

    private final IOuterCustomerCenterService outerCustomerCenterService;

    private final IOuterMetadataService outerMetadataService;

    private final IOuterCustomerService outerCustomerService;

    @Override
    public PosPhase[] supportedPhases() {
        return new PosPhase[]{PosPhase.register};
    }

    @Override
    public Result onContinueError(Exception e, PosPhase phase, PosDataEntryTemplate param, PosRegisterCaseRequest businessModel, BusinessHandleContext context) {
        throw CommonException.byErrorAndCause(POS_SERVICE_UNAVAILABLE, e);
    }

    @Override
    public Result onContinueSucceed(Result continueResult, PosPhase phase, PosDataEntryTemplate param, PosRegisterCaseRequest businessModel, BusinessHandleContext context) {
        return continueResult;
    }

    @Override
    public Result onStop(PosPhase phase, PosDataEntryTemplate param, PosRegisterCaseRequest businessModel, BusinessHandleContext context) {
        return null;
    }

    @Override
    public FlowStrategy handle(PosPhase phase, PosDataEntryTemplate param, PosRegisterCaseRequest businessModel, BusinessHandleContext context) {
        PosRegisterBeforeEvent posRegisterBeforeEvent = new PosRegisterBeforeEvent(context.getOrder().getOrderNo());
        applicationContext.publishEvent(posRegisterBeforeEvent);
        convertUimodel2PosCase(param, businessModel);
        businessModel.setExternalTransactionNo(posRegisterBeforeEvent.getThirdPartyNo());
        businessModel.setExternalSerialNo(posRegisterBeforeEvent.getThirdPartyNo2());
        businessModel.setPayOrderNo(posRegisterBeforeEvent.getPayOrderNo());

        if (Boolean.TRUE.equals(posRegisterBeforeEvent.isPaySuccess())) {
            /*
            后续如果按配置来，需要把payment-gateway的 payChannel转成 bcp的payMethod,有个映射关系，
            accountType的获取，需要根据dict(collectionPaymentMethod)的配置来获取accountType,目前demo只做STRIPE这一个payChannel
            */
            PosCaseCollectionAccountInfo collectionAccountInfo = new PosCaseCollectionAccountInfo();
            collectionAccountInfo.setAccountType(AccountTypeEnum.STRIPE_PAY_WALLET);
            collectionAccountInfo.setCollectionMethod(PayMethodEnum.STRIPE_PAY);
            businessModel.setCollectionAccount(collectionAccountInfo);


            CustomerCenterIndividualResponse customerInfo = outerCustomerCenterService.getChannelCustomerInfo(Long.parseLong(context.getParams().get("userId").toString()));
            PosPayerInfo posPayerInfo = PosCaseMapper.MAPPER.convertCustomer(customerInfo);


            PartyCustomerCreateRequest partyCustomerCreateRequest = PosCaseMapper.MAPPER.convertCustomerCreate(posPayerInfo);
            partyCustomerCreateRequest.setTransType(param.getPosItemType());
            partyCustomerCreateRequest.setTransNo(String.valueOf(System.currentTimeMillis()));
            partyCustomerCreateRequest.setRoleType(PolicyCustomerEnum.PAYEE);
            PartyCustomerCreateResponse partyCustomer = outerCustomerService.createPartyCustomer(partyCustomerCreateRequest);

            posPayerInfo.setCollectionAccount(collectionAccountInfo);
            posPayerInfo.setPartyId(partyCustomer.getPartyId());
            posPayerInfo.setCustomerId(partyCustomer.getPartyId());
            businessModel.setPosPayerList(Arrays.asList(posPayerInfo));
        }
        return FlowStrategy.Continue;
    }

}