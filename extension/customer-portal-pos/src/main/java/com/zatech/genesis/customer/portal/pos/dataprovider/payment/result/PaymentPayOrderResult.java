package com.zatech.genesis.customer.portal.pos.dataprovider.payment.result;

import com.zatech.genesis.payment.gateway.api.response.PayOrderResp;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.FailureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.SuccessResult;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class PaymentPayOrderResult implements Result {

    private PayOrderResp payInfo;

    @Override
    public boolean isSuccess() {
        return true;
    }

    @Override
    public SuccessResult succeed() {
        return SuccessResult.from(this);
    }

    @Override
    public FailureResult failed() {
        return null;
    }
}
