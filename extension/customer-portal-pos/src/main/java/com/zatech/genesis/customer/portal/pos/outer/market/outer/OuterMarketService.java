/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.market.outer;

import com.zatech.genesis.customer.portal.pos.outer.market.outer.fallback.OuterMarketServiceFallbackFactory;
import com.zatech.genesis.customer.portal.pos.outer.market.request.GreenCardRequest;
import com.zatech.genesis.customer.portal.pos.outer.market.response.GoodsBasicInfoResp;
import com.zatech.genesis.customer.portal.pos.outer.market.response.GreenCardResponse;
import com.zatech.genesis.market.api.calculate.request.sapremium.SaPremiumCalcInitialPeriodRequest;
import com.zatech.genesis.market.api.calculate.response.sapremium.CalcSaPremiumMultiPeriodResponse;
import com.zatech.genesis.market.api.structure.request.PackageApplicationElementsEnumRequest;
import com.zatech.genesis.market.api.structure.request.QueryEffectiveDateRequest;
import com.zatech.genesis.market.api.structure.request.QueryExpiryDateRequest;
import com.zatech.genesis.market.api.structure.request.QueryGoodsRelatingListRequest;
import com.zatech.genesis.market.api.structure.request.QueryGoodsRelatingRequest;
import com.zatech.genesis.market.api.structure.response.GoodsRelatingResponse;
import com.zatech.genesis.market.api.structure.response.PackageApplicationElementsEnumResponse;

import io.swagger.v3.oas.annotations.Operation;

import java.util.Date;
import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "fm-za-market-endpoint", url = "${genesis-feign-market:localhost}", fallbackFactory = OuterMarketServiceFallbackFactory.class)
public interface OuterMarketService {

    @PostMapping(value = "/api/v2/market/structure/expiryDate")
    Date queryExpiryDate(@RequestBody @Valid QueryExpiryDateRequest request);

    @PostMapping(value = {"/api/v2/market/structure/effectiveDate"}, headers = {"Content-Type=application/json"})
    Date queryEffectiveDate(@RequestBody @Valid QueryEffectiveDateRequest var1);

    @PostMapping(value = "/api/v2/market/structure/queryGoodsRelating", headers = {"Content-Type=application/json"})
    GoodsRelatingResponse queryGoodsRelating(@RequestBody @NotNull QueryGoodsRelatingRequest request);

    @PostMapping(value = "/api/v2/market/structure/queryGoodsRelatingList")
    @Operation(summary = "Api for query market structure info list")
    List<GoodsBasicInfoResp> queryGoodsRelatingList(QueryGoodsRelatingListRequest requestDTO);

    @PostMapping(value = "/api/v2/market/structure/application-elements/enum-items")
    PackageApplicationElementsEnumResponse queryPackageApplicationElementEnumItem(@RequestBody @NotNull PackageApplicationElementsEnumRequest request);

    @PostMapping("/api/v2/calculate/sa-premium/batch")
    List<CalcSaPremiumMultiPeriodResponse> batchCalculateSaPremium(@RequestBody @Valid @NotNull List<SaPremiumCalcInitialPeriodRequest> requests);

    @PostMapping(value = {"/api/v2/calculate/green-card"})
    GreenCardResponse calculateGreenCardFee(@RequestBody List<GreenCardRequest> request);

}
