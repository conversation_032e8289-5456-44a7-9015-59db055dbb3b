package com.zatech.genesis.customer.portal.pos.service;

import com.google.common.collect.Lists;
import com.zatech.genesis.customer.portal.pos.outer.IOuterMetadataService;
import com.zatech.genesis.metadata.api.bizdict.request.QueryBizDictTenantRequest;
import com.zatech.genesis.metadata.api.bizdict.response.BizDictTenantResponse;
import com.zatech.genesis.portal.toolbox.share.model.EnumItem;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class MetadataService {

    @Resource
    private IOuterMetadataService iOuterMetadataService;

    public Map<String, List<EnumItem>> loadItems(List<String> dictKeys) {
        QueryBizDictTenantRequest request = new QueryBizDictTenantRequest();
        request.setDictKeys(dictKeys);
        final var resp = iOuterMetadataService.queryBizDictTenant(request);
        return convert(resp);
    }

    private Map<String, List<EnumItem>> convert(List<BizDictTenantResponse> responses) {
        return Optional.ofNullable(responses).orElseGet(Lists::newArrayList).stream()
            .collect(Collectors.groupingBy(BizDictTenantResponse::getDictKey,
                Collectors.mapping(this::convert, Collectors.toList())));
    }

    private EnumItem convert(BizDictTenantResponse x) {
        return new EnumItem(x.getEnumItemName(), x.getDictValueName());
    }
}
