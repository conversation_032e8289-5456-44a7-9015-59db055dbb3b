/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.steps.stepfilter;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.step.StepCategoryEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.step.StepFilter;
import com.zatech.genesis.customer.portal.pos.dataprovider.steps.result.PosPageStep;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;

import org.apache.commons.lang3.ObjectUtils;

/**
 * @Author: weizhen.kong
 */
public abstract class AbstractPosStepFilter implements StepFilter<PosDataEntryTemplate, PosPageStep> {

    protected boolean preCheck(PosDataEntryTemplate dataEntry) {
        //如果是投被保人基本信息变更，没有人的信息的情况下算不出收退费数据
        if ((TransTypeEnum.POS_HOLDER_INFO_CHANGES.equals(dataEntry.getPosItemType()) || TransTypeEnum.POS_INSURED_INFO_CHANGES.equals(dataEntry.getPosItemType())) && ObjectUtils.allNull(dataEntry.getPolicyInsured(), dataEntry.getPolicyHolder())) {
            return Boolean.FALSE;
        }

        //没有保全信息也是无法计算出是否要收退费
        if (dataEntry.getPosApplicationInfo() == null) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public StepCategoryEnum category() {
        return StepCategoryEnum.POS;
    }

}