/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.market.response;

import java.util.List;

import lombok.ToString;
import lombok.experimental.Accessors;

@ToString
@Accessors(chain = true)
public class ApplicationElementItemsResponse {

    private Long packageId;

    private List<ApplicationElementsItem> holderItems;

    private List<ApplicationElementsItem> insuredItems;

    private List<ApplicationElementsItem> beneficiaryItems;

    private List<ApplicationElementsItem> assigneeItems;

    private List<ApplicationElementsItem> secondaryLifeInsuredItems;

    private List<ApplicationElementsItem> payerItems;

    private List<ApplicationElementsItem> objectItems;

    private List<ApplicationElementsItem> nomineeItems;

    private List<ApplicationElementsItem> consenteeItems;

    private List<ApplicationElementsItem> trusteeItems;

    private List<ApplicationElementsItem> policyItems;

    private List<ApplicationElementsItem> beneficialOwnerItems;

    private List<ApplicationElementsItem> premiumFunderItems;

    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }

    public List<ApplicationElementsItem> getHolderItems() {
        return holderItems;
    }

    public void setHolderItems(List<ApplicationElementsItem> holderItems) {
        this.holderItems = holderItems;
    }

    public List<ApplicationElementsItem> getInsuredItems() {
        return insuredItems;
    }

    public void setInsuredItems(List<ApplicationElementsItem> insuredItems) {
        this.insuredItems = insuredItems;
    }

    public List<ApplicationElementsItem> getBeneficiaryItems() {
        return beneficiaryItems;
    }

    public void setBeneficiaryItems(List<ApplicationElementsItem> beneficiaryItems) {
        this.beneficiaryItems = beneficiaryItems;
    }

    public List<ApplicationElementsItem> getAssigneeItems() {
        return assigneeItems;
    }

    public void setAssigneeItems(List<ApplicationElementsItem> assigneeItems) {
        this.assigneeItems = assigneeItems;
    }

    public List<ApplicationElementsItem> getSecondaryLifeInsuredItems() {
        return secondaryLifeInsuredItems;
    }

    public void setSecondaryLifeInsuredItems(List<ApplicationElementsItem> secondaryLifeInsuredItems) {
        this.secondaryLifeInsuredItems = secondaryLifeInsuredItems;
    }

    public List<ApplicationElementsItem> getPayerItems() {
        return payerItems;
    }

    public void setPayerItems(List<ApplicationElementsItem> payerItems) {
        this.payerItems = payerItems;
    }

    public List<ApplicationElementsItem> getObjectItems() {
        return objectItems;
    }

    public void setObjectItems(List<ApplicationElementsItem> objectItems) {
        this.objectItems = objectItems;
    }

    public List<ApplicationElementsItem> getNomineeItems() {
        return nomineeItems;
    }

    public void setNomineeItems(List<ApplicationElementsItem> nomineeItems) {
        this.nomineeItems = nomineeItems;
    }

    public List<ApplicationElementsItem> getConsenteeItems() {
        return consenteeItems;
    }

    public void setConsenteeItems(List<ApplicationElementsItem> consenteeItems) {
        this.consenteeItems = consenteeItems;
    }

    public List<ApplicationElementsItem> getTrusteeItems() {
        return trusteeItems;
    }

    public void setTrusteeItems(List<ApplicationElementsItem> trusteeItems) {
        this.trusteeItems = trusteeItems;
    }

    public List<ApplicationElementsItem> getPolicyItems() {
        return policyItems;
    }

    public void setPolicyItems(List<ApplicationElementsItem> policyItems) {
        this.policyItems = policyItems;
    }

    public List<ApplicationElementsItem> getBeneficialOwnerItems() {
        return beneficialOwnerItems;
    }

    public void setBeneficialOwnerItems(List<ApplicationElementsItem> beneficialOwnerItems) {
        this.beneficialOwnerItems = beneficialOwnerItems;
    }

    public List<ApplicationElementsItem> getPremiumFunderItems() {
        return premiumFunderItems;
    }

    public void setPremiumFunderItems(List<ApplicationElementsItem> premiumFunderItems) {
        this.premiumFunderItems = premiumFunderItems;
    }

}
