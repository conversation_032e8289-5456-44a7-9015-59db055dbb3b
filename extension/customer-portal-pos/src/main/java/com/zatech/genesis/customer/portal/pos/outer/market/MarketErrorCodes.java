/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.market;

import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;
import com.zatech.genesis.portal.toolbox.exception.mapping.SourceErrorMapping;

/**
 * <AUTHOR>
 * @create 2024/1/24 10:58
 **/
public enum MarketErrorCodes implements IErrorCode {
    goods_not_found,

    package_not_found,

    @SourceErrorMapping(errorCodes = {"calculator.engine.rate.table.not.match"})
    CALCULATOR_RATE_TABLE_NOT_MATCH;

    @Override
    public String getModuleName() {
        return "market";
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}
