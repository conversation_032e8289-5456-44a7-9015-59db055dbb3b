/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.template.poscase;

import com.zatech.genesis.customer.portal.pos.template.AttachmentInfo;
import com.zatech.genesis.customer.portal.pos.template.PayResult;
import com.zatech.genesis.customer.portal.pos.template.PosBaseModel;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate.PolicyInsured;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate.PosApplicationInfo;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.Project;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.UIModel;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;
import com.zatech.genesis.portal.lowcode.framework.common.enums.ModuleTypeEnum;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
@UIModel(
        name = "PosInsuredChangesDataEntry",
        project = @Project(name = ModuleTypeEnum.customerPortal, productCategories = {FreeMartProductCategoryEnum.ALL}),
        desc = "Ui-model for pos pos_insured_info_changes case.")
public class PosInsuredChangesDataEntryTemplate extends PosBaseModel {

    private Long policyProductId;

    private PosApplicationInfo posApplicationInfo;

    private PolicyInsured policyInsured;

    private List<AttachmentInfo> attachmentInfo;

    private PayResult payResult;

}