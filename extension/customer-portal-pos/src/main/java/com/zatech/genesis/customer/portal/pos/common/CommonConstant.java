/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.common;

/**
 * @Author: weizhen.kong
 */
public class CommonConstant {

    private CommonConstant() {
    }

    public static final String SCHEMA_ENUM_NODE = "x-enum-display";

    public static final String APPLICATION_ELEMENT_CONFIG = "applicationConfig";

    public static final String HOLDER_ITEMS_ROLE = "holderItems";

    public static final String INSURED_ITEMS_ROLE = "insuredItems";

    //countryCode的展示有些特殊，取的是dict配置的itemExtend2字段
    public static final String COUNTRY_CODE = "countryCode";

    public static final String EDITABLE_SUFFIX = "Editable";

    public static final String SCHEMA_EDITABLE = "x-editable";

    public static final String SCHEMA_VISIBLE = "x-visible";

}