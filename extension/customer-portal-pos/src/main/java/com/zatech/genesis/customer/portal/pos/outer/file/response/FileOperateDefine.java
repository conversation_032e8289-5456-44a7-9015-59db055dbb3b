/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.file.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(exclude = {"content"})
@NoArgsConstructor
public class FileOperateDefine {

    private static final long serialVersionUID = 1L;

    /**
     * 文件统一编码
     */
    @Schema(name = "文件统一编码")
    private String fileUniqueCode;

    /**
     * 存储媒介的url (不保证全局唯一)
     */
    @Schema(name = "存储媒介的url (不保证全局唯一)")
    private String fileInsideUrl;

    /**
     * 文件名字+文件后缀,如：身份证.jpg
     */
    @Schema(name = "文件名字+文件后缀,如：身份证.jpg")
    private String fileName;

    /**
     * 文件类型
     */
    @Schema(name = "文件类型")
    private String fileFormat;

    /**
     * 文件存储信息
     */
    @Schema(name = "文件存储信息")
    private byte[] content;

    @Schema(name = "临时url操作请求方式")
    private String httpMethod;

    @Schema(name = "文件零时url时效")
    private Date expiration;

    @Schema(name = "临时时Url")
    private String url;

    @Schema(name = "文件路径")
    private String filePath;

}
