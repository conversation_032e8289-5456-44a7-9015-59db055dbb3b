/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.market.response;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

import lombok.experimental.Accessors;

import org.apache.commons.collections4.CollectionUtils;

@Accessors(chain = true)
public class EnumItem {

    private Object code;

    private String name;

    private String key;

    private String extension1;

    private String extension2;

    private String extension3;

    private String extension4;

    private String extension5;

    private String enumItemName;

    private Boolean required;

    private List<EnumItem> children = Lists.newArrayList();

    public EnumItem(Object code, String name) {
        this.code = code;
        this.name = name;
    }

    public EnumItem(Object code, String name, String enumItemName, String key) {
        this.code = code;
        this.name = name;
        this.enumItemName = enumItemName;
        this.key = key;
    }

    public int size() {
        if (CollectionUtils.isEmpty(children)) {
            return 1;
        }
        return children.stream().mapToInt(EnumItem::size).sum();
    }

    public Object getCode() {
        return code;
    }

    public void setCode(Object code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getExtension1() {
        return extension1;
    }

    public void setExtension1(String extension1) {
        this.extension1 = extension1;
    }

    public String getExtension2() {
        return extension2;
    }

    public void setExtension2(String extension2) {
        this.extension2 = extension2;
    }

    public String getExtension3() {
        return extension3;
    }

    public void setExtension3(String extension3) {
        this.extension3 = extension3;
    }

    public String getExtension4() {
        return extension4;
    }

    public void setExtension4(String extension4) {
        this.extension4 = extension4;
    }

    public String getExtension5() {
        return extension5;
    }

    public void setExtension5(String extension5) {
        this.extension5 = extension5;
    }

    public String getEnumItemName() {
        return enumItemName;
    }

    public void setEnumItemName(String enumItemName) {
        this.enumItemName = enumItemName;
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }

    public List<EnumItem> getChildren() {
        return children;
    }

    public void setChildren(List<EnumItem> children) {
        this.children = children;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EnumItem enumItem = (EnumItem) o;
        return Objects.equals(code, enumItem.code);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code);
    }

}
