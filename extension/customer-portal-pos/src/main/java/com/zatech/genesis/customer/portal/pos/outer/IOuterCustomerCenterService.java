/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer;

import com.zatech.genesis.customer.portal.pos.outer.fallback.OuterCustomerCenterServiceFallbackFactory;
import com.zatech.genesis.customer.portal.pos.outer.response.CustomerCenterIndividualResponse;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * @Author: weizhen.kong
 */
@FeignClient(name = "zatech-pos-customer-center", url = "${genesis-feign-customer-center}", fallbackFactory = OuterCustomerCenterServiceFallbackFactory.class)
public interface IOuterCustomerCenterService {

    @GetMapping(value = "/api/v1/individual/channel-customers/{channel-customer-id}")
    CustomerCenterIndividualResponse getChannelCustomerInfo(@PathVariable("channel-customer-id") Long channelCustomerId);

}