/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.datavalidate.poscase;

import com.zatech.gaia.resource.graphene.posonline.PosStatusStepEnum;
import com.zatech.genesis.customer.portal.pos.datavalidate.poscase.result.PosValidateResult;
import com.zatech.genesis.customer.portal.pos.exception.PosPortalErrorCode;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPosService;
import com.zatech.genesis.customer.portal.pos.outer.request.PosRegisterCaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.PosValidateCaseResponse;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;
import com.zatech.genesis.customer.portal.pos.util.UiModelReflectUtil;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.datavalidate.DataValidateContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.datavalidate.DataValidateKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.datavalidate.IDataValidator;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.datavalidate.UIModelDataValidator;
import com.zatech.genesis.portal.toolbox.exception.CommonException;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

/**
 * @Author: weizhen.kong
 */
@Slf4j
@UIModelDataValidator(name = "posCaseValidate", kind = DataValidateKind.validate, desc = "校验是否可以进行保全项操作")
@AllArgsConstructor
public class PosItemValidator implements IDataValidator<PosDataEntryTemplate, PosValidateResult> {

    private final IOuterPosService outerPosService;

    @Override
    public PosValidateResult validate(PosDataEntryTemplate param, DataValidateContext dataValidateContext) {
        PosValidateResult posValidateResult = new PosValidateResult();

        PosRegisterCaseRequest posCase = new PosRegisterCaseRequest();
        UiModelReflectUtil.convertUimodel2PosCase(param, posCase);
        if (param.getPosApplicationInfo() == null) {
            posCase.setStatusStep(PosStatusStepEnum.REGISTER);
        }

        PosValidateCaseResponse response = outerPosService.validateCase(posCase);
        if (ObjectUtils.allNull(response) || ObjectUtils.anyNull(response.getValidationSuccess())) {
            posValidateResult.setPass(false);
            posValidateResult.setMessages(new PosValidateResult.ValidateMessage(PosPortalErrorCode.POS_SERVICE_UNAVAILABLE.getErrorCode(), CommonException.byError(PosPortalErrorCode.POS_SERVICE_UNAVAILABLE).getMessage()));
            return posValidateResult;
        }
        if (response.getValidationSuccess()) {
            posValidateResult.setPass(Boolean.TRUE);
            return posValidateResult;
        }

        if (CollectionUtils.isEmpty(response.getValidationErrors())) {
            posValidateResult.setMessages(new PosValidateResult.ValidateMessage(PosPortalErrorCode.POS_SERVICE_UNAVAILABLE.getErrorCode(), CommonException.byError(PosPortalErrorCode.POS_SERVICE_UNAVAILABLE).getMessage()));
        } else {
            posValidateResult.setMessages(new PosValidateResult.ValidateMessage(response.getValidationErrors().get(0).getErrCode(), response.getValidationErrors().get(0).getMessage()));
        }

        posValidateResult.setPass(Boolean.FALSE);
        return posValidateResult;
    }

}