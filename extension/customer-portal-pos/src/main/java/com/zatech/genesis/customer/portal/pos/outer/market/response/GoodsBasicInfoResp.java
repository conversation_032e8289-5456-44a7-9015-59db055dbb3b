/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.market.response;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

@Data
public class GoodsBasicInfoResp {

    @Schema(title = "商品id")
    private Long goodsId;

    @Schema(title = "商品code，不包含版本，同code")
    private String goodsCode;

    @Schema(title = "商品Code，不包含版本")
    private String code;

    @Schema(title = "商品版本：默认从V01开始")
    private String goodsVersion;

    /**
     * {@link com.zatech.gaia.resource.components.enums.market.GoodStatusEnum}
     */
    @Schema(title = "商品状态")
    private String goodsStatus;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "商品类别id")
    private Integer categoryId;

    @Schema(title = "商品类别name")
    private String categoryName;

    @Schema(title = "营销商品类型-租户级别自定义")
    private String goodsType;

    @Schema(title = "销售类型")
    private String salesType;

    @Schema(title = "商品简介")
    private String goodsDesc;

    @Schema(title = "usageBased goods标识")
    private YesNoEnum usageBased;

    @Schema(title = "goods是否配置了协议单标识")
    private YesNoEnum isMasterPolicy;

    @Schema(title = "创建时间")
    private Date gmtCreated;

    @Schema(title = "更新时间")
    private Date gmtModified;

    @Schema(title = "No Claim Discount Configured Code")
    private String ncdConfiguredCode;

}
