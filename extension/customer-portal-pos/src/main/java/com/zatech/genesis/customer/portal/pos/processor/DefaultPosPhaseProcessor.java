/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.processor;

import com.zatech.genesis.customer.portal.biz.common.uimodel.event.CreateOrderPosCaseEvent;
import com.zatech.genesis.customer.portal.pos.businesshandle.poscase.result.PosRegisterCaseResult;
import com.zatech.genesis.customer.portal.pos.datavalidate.poscase.result.PosValidateResult;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPosService;
import com.zatech.genesis.customer.portal.pos.outer.request.PosCaseBaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.request.PosRegisterCaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.PosRegisterCaseResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.IPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PosPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.phase.IUIModelPhasePostProcessor;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.octopus.core.util.JacksonUtil;

import java.util.Optional;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;


/**
 * @Author: weizhen.kong
 * 发起调用pos的动作在这里，这是公共的逻辑
 */
@Slf4j
@Component
public class DefaultPosPhaseProcessor implements IUIModelPhasePostProcessor<PosRegisterCaseRequest> {

    @Resource
    private IOuterPosService outerPosService;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public IPhase[] forPhases() {
        return new IPhase[]{PosPhase.register};
    }

    @Override
    public Result process(IPhase phase, PosRegisterCaseRequest businessModel, BusinessHandleContext context) {
        log.info("Register pos case request: {}", JacksonUtil.toJSONString(businessModel));
        PosRegisterCaseResult posRegisterCaseResult = new PosRegisterCaseResult();
        PosRegisterCaseResponse caseResponse = outerPosService.applyCase(businessModel);
        log.info("Register pos case, pos response: {}", JacksonUtil.toJSONString(caseResponse));
        if (StringUtils.isEmpty(caseResponse.getCaseNo())) {
            log.warn("Pos register case failed, policyNo: {}, transType: {}, pos result: {}", businessModel.getPolicyNo(), businessModel.getPosTransType(), JacksonUtil.toJSONString(caseResponse));
            Optional.ofNullable(caseResponse.getPosRuleDecision()).filter(posRule -> CollectionUtils.isNotEmpty(posRule.getRuleDecisionDetailList()))
                    .ifPresent(posRule -> posRegisterCaseResult.setMessages(posRule.getRuleDecisionDetailList().stream().map(detail -> new PosValidateResult.ValidateMessage(detail.getMsgCode(), detail.getMsgValue())).collect(Collectors.toList())));
            return posRegisterCaseResult;
        }
        posRegisterCaseResult.setCaseNo(caseResponse.getCaseNo());

        applicationContext.publishEvent(new CreateOrderPosCaseEvent(context.getOrder().getOrderId(), caseResponse.getCaseNo(), businessModel.getPolicyNo(), businessModel.getTransactionList().stream().map(PosCaseBaseRequest.PosTransaction::getPosTransType).collect(Collectors.toList())));
        return posRegisterCaseResult;
    }
}