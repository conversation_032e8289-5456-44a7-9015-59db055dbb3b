/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.event;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;

import lombok.Getter;

/**
 * @Author: weizhen.kong
 */
@Getter
public class PolicyCustomerLoadEvent extends AbstractEvent {

    private String policyNO;

    private String orderNo;

    private Long moduleId;

    private JsonMap jsonMap;

    private TransTypeEnum posItemType;

    public PolicyCustomerLoadEvent(String policyNO, String orderNo, Long moduleId, TransTypeEnum posItemType, JsonMap jsonMap) {
        super(policyNO);
        this.policyNO = policyNO;
        this.orderNo = orderNo;
        this.moduleId = moduleId;
        this.jsonMap = jsonMap;
        this.posItemType = posItemType;
    }

}