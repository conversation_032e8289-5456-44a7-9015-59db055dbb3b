/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.response;

import com.zatech.gaia.resource.components.enums.posonline.PosRuleCategoryEnum;
import com.zatech.gaia.resource.components.enums.posonline.PosRuleDecisionEnum;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
public class PosValidateCaseResponse {

    private Boolean validationSuccess;

    private List<CaseValidationErrorResponse> validationErrors;

    private String caseNo;

    private PosRuleDecisionResponse posRuleDecision;

    @Getter
    @Setter
    public static class CaseValidationErrorResponse {

        private String errCode;

        private String message;

        private String systemException;
    }

    @Getter
    @Setter
    public static class PosRuleDecisionResponse {

        private boolean uwDecisionFlag;

        private PosRuleDecisionEnum uwDecisionType;

        private boolean complianceDecisionFlag;

        private PosRuleDecisionEnum complianceDecisionType;

        private List<PosRuleDecisionMessageResponse> warningMessageList;

        private List<PosRuleDecisionMessageResponse> uwMessageList;

        private List<PosRuleDecisionMessageResponse> complianceMessageList;

        private List<RuleDecisionDetailResponse> ruleDecisionDetailList;

    }

    @Getter
    @Setter
    public static class PosRuleDecisionMessageResponse {

        private String code;

        private String message;
    }

    @Getter
    @Setter
    public static class RuleDecisionDetailResponse {

        private String ruleCode;

        private PosRuleCategoryEnum ruleCategory;

        private PosRuleDecisionEnum decisionType;

        private String msgCode;

        private String msgValue;

    }

}