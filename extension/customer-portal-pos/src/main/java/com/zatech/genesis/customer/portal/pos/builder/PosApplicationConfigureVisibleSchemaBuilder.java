/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.builder;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.ISchemaNodeBuilder;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildMetaInfo;

import io.swagger.v3.oas.models.media.Schema;

import java.time.ZonedDateTime;

import org.springframework.stereotype.Component;

import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.SCHEMA_VISIBLE;

/**
 * @Author: weizhen.kong
 */
@Component
public class PosApplicationConfigureVisibleSchemaBuilder implements ISchemaNodeBuilder<Void> {
    @Override
    public Void buildParam(SchemaNodeBuildContext schemaNodeBuildContext, SchemaNodeBuildMetaInfo schemaNodeBuildMetaInfo) {
        return null;
    }

    @Override
    public Schema buildSchema(Schema schema, Void param, SchemaNodeBuildContext schemaNodeBuildContext, SchemaNodeBuildMetaInfo schemaNodeBuildMetaInfo) {
        schema._default(ZonedDateTime.now()).addExtension(SCHEMA_VISIBLE, Boolean.FALSE);
        return schema;
    }
}