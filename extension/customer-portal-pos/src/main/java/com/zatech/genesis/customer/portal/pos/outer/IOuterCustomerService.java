/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer;

import com.zatech.gaia.resource.customer.scenario.share.dto.base.CustOptionEnum;
import com.zatech.genesis.customer.portal.pos.outer.fallback.OuterCustomerServiceFallbackFactory;
import com.zatech.genesis.customer.portal.pos.outer.request.PartyCustomerCreateRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.PartyCustomerCreateResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PolicyCustomerResponse;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: weizhen.kong
 */
@FeignClient(name = "zatech-inner-customer", url = "${genesis-feign-customer}", fallbackFactory = OuterCustomerServiceFallbackFactory.class)
public interface IOuterCustomerService {

    @GetMapping(value = {"/api/v2/party/customers/{customer-id}"})
    PolicyCustomerResponse queryCustomer(@PathVariable(value = "customer-id") Long customerId, List<CustOptionEnum> options);

    @PostMapping(value = {"/api/v2/party/customers"})
    PartyCustomerCreateResponse createPartyCustomer(@RequestBody PartyCustomerCreateRequest posPayerInfo);

}