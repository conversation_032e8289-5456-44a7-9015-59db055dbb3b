package com.zatech.genesis.customer.portal.pos.dataprovider.payment;

import com.zatech.gaia.resource.components.enums.bcp.ArApEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayChannelEnum;
import com.zatech.genesis.customer.portal.api.payment.request.OrderPaymentRequest;
import com.zatech.genesis.customer.portal.pos.dataprovider.payment.request.PaymentRequest;
import com.zatech.genesis.customer.portal.pos.dataprovider.payment.result.PaymentPayOrderResult;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPaymentService;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPolicyService;
import com.zatech.genesis.policy.api.response.PolicyChannelResponse;
import com.zatech.genesis.policy.api.response.PolicyProductResponse;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProviderKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.UIModelDataProvider;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;
import com.zatech.octopus.core.util.JacksonUtil;

import java.util.Date;
import java.util.Optional;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationContext;

import static com.zatech.gaia.resource.components.enums.paymentgateway.TradingScenarioEnum.REDIRECT_URL_PAYMENT;

@AllArgsConstructor
@Slf4j
@UIModelDataProvider(
        name = "paymentUrl",
        productCategories = {FreeMartProductCategoryEnum.ALL},
        kind = DataProviderKind.data,
        desc = "获取支付链接")
public class PaymentDataProvider implements IDataProvider<PaymentRequest, PaymentPayOrderResult> {

    private final ApplicationContext applicationContext;

    private final IOuterPaymentService paymentService;

    private final IOuterPolicyService outerPolicyService;

    @Override
    public PaymentPayOrderResult provide(PaymentRequest paymentRequest, DataProvideContext dataProvideContext) {
        PolicyResponse policyResponse = outerPolicyService.queryPolicy(paymentRequest.getPolicyNo(), null, false);
        String channelCode = fetchChannelCode(policyResponse);
        final var pay = paymentService.pay(buildPaymentRequest(channelCode, dataProvideContext));
        final var payment = new OrderPaymentRequest(dataProvideContext.getOrder().getOrderNo(),
                pay.getPayOrderNo(),
                pay.getAmount(),
                pay.getCurrency().name(),
                pay.getPayStatus().name(),
                String.valueOf(dataProvideContext.getParams().get("payChannel")),
                new Date(),
                String.valueOf(dataProvideContext.getParams().get("payMethod")));
        applicationContext.publishEvent(payment);
        return new PaymentPayOrderResult().setPayInfo(pay);
    }

    private String fetchChannelCode(PolicyResponse policyResponse) {
        Optional<PolicyProductResponse> mainProduct = policyResponse.getPolicyProductList().stream().filter(product -> product.getMainId() == null).findFirst();
        if (mainProduct.isPresent() && CollectionUtils.isNotEmpty(mainProduct.get().getPolicyChannelList())) {
            return mainProduct.get().getPolicyChannelList().get(0).getChannelCode();
        }
        return Optional.ofNullable(policyResponse.getPolicyChannel()).map(PolicyChannelResponse::getChannelCode).orElse(null);
    }

    /**
     * eg{
     * "email": "<EMAIL>",
     * "phone": "***********",
     * "channelCode": "0101",
     * "payChannel": "WSPAY",
     * "payMethod": "CARD",
     * "amount": "100.1",
     * "currency": "USD",
     * "payBusinessType": "POS_HOLDER_INFO_CHANGES",
     * "payerName": "zhangsan",
     * "description": "zatech to pay",
     * "frontendSuccessUrl": "www.baidu.com",
     * "frontendCancelUrl": "",
     * "frontendErrorUrl": "",
     * "frontendReturnUrl": "",
     * "extensionParams":{
     * "CustomerFirstName": "123",
     * "CustomerLastName": "123",
     * "CustomerCity": "123",
     * "CustomerAddress": "123 1123",
     * "CustomerZIP": "123",
     * }
     * }
     *
     * @param channelCode
     * @param context
     * @return
     */
    private PaymentRequest buildPaymentRequest(String channelCode, DataProvideContext context) {
        final var orderNo = context.getOrder().getOrderNo();
        final var req = JacksonUtil.toObject(JacksonUtil.toJSONString(context.getParams()), PaymentRequest.class);
        PaymentRequest payInfo = new PaymentRequest();
        payInfo.setEmail(req.getEmail());
        payInfo.setPhone(req.getPhone());
        payInfo.setChannelCode(Optional.ofNullable(channelCode).orElse(req.getChannelCode()));
        payInfo.setPayChannel(req.getPayChannel());
        payInfo.setPayMethod(req.getPayMethod());
        payInfo.setAmount(req.getAmount());
        payInfo.setCurrency(req.getCurrency());
        payInfo.setPayBusinessType(req.getPayBusinessType());
        payInfo.setPayBusinessNo(orderNo);
        payInfo.setZoneId(req.getZoneId());
        payInfo.setPayerName(req.getPayerName());
        payInfo.setArAp(ArApEnum.Ar);
        payInfo.setExtensionParams(req.getExtensionParams());
        payInfo.setDescription(req.getDescription());
        if (req.getPayChannel() == PayChannelEnum.WSPAY) {
            payInfo.setFrontendSuccessUrl(req.getFrontendSuccessUrl());
            payInfo.setFrontendCancelUrl(req.getFrontendCancelUrl());
            payInfo.setFrontendErrorUrl(req.getFrontendErrorUrl());
        } else if (req.getPayChannel() == PayChannelEnum.STRIPE) {
            payInfo.setFrontendSuccessUrl(req.getFrontendReturnUrl());
            payInfo.setFrontendCancelUrl(req.getFrontendCancelUrl());
            payInfo.setTradingScenario(REDIRECT_URL_PAYMENT);
        } else {
            payInfo.setFrontendReturnUrl(req.getFrontendReturnUrl());
        }
        log.info("Request payment info: {}", JacksonUtil.toJSONString(payInfo));
        return payInfo;
    }
}
