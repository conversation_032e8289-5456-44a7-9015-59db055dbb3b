package com.zatech.genesis.customer.portal.pos.dataprovider.payment.result;

import com.zatech.genesis.customer.portal.pos.template.poscase.PaymentPayeeTemplate;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.FailureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.SuccessResult;
import com.zatech.genesis.portal.toolbox.share.json.Json;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PayerResult implements Result {

    private Json schema;

    private PaymentPayeeTemplate result;

    public Json getSchema() {
        return schema;
    }

    public void setSchema(Json schema) {
        this.schema = schema;
    }

    @Override
    public boolean isSuccess() {
        return true;
    }

    @Override
    public SuccessResult succeed() {
        return SuccessResult.from(this);
    }

    @Override
    public FailureResult failed() {
        return null;
    }
}
