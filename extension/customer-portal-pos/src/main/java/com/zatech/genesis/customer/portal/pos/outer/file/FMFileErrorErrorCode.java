/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.file;

import com.zatech.genesis.portal.toolbox.exception.StandardErrorCode;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

public enum FMFileErrorErrorCode implements IErrorCode {

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    UPLOAD_FORMAT_NOT_SUPPORTED,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    UPLOAD_BAD_CONTENT,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    UPLOAD_FILE_TOO_LARGE,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    UPLOAD_FILENAME_TOO_LONG,

    UPLOAD_UNKNOWN;

    @Override
    public String getModuleName() {
        return "file";
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}
