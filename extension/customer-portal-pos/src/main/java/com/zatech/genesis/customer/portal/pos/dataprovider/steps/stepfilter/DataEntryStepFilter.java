/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.steps.stepfilter;

import com.zatech.genesis.customer.portal.pos.dataprovider.steps.result.PosPageStep;
import com.zatech.genesis.customer.portal.pos.dataprovider.steps.result.PosPageStepEnum;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @Author: weizhen.kong
 */
@Component
@Order(0)
public class DataEntryStepFilter extends AbstractPosStepFilter {

    @Override
    public PosPageStep filter(PosDataEntryTemplate dataEntry) {
        return new PosPageStep(PosPageStepEnum.DATA_ENTRY, Boolean.TRUE);
    }
}