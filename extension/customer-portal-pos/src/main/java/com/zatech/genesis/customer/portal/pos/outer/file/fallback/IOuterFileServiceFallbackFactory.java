/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.file.fallback;

import com.zatech.genesis.customer.portal.pos.outer.file.IOuterFileService;
import com.zatech.genesis.customer.portal.pos.outer.file.request.FileRequest;
import com.zatech.genesis.customer.portal.pos.outer.file.response.FileOperateDefine;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;
import com.zatech.octopus.module.web.dto.ResultBase;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

@Component("fm-iOuterFileServiceFallbackFactory")
@Slf4j
public class IOuterFileServiceFallbackFactory extends AbstractFallbackFactory<IOuterFileService> {

    @Override
    public IOuterFileService create(Throwable cause) {
        return new IOuterFileService() {

            @Override
            public ResultBase<FileOperateDefine> operate(FileRequest req) {
                throw outerServiceException(cause,
                    String.format("fileName: %s", req.getFileName()));
            }

        };
    }

}
