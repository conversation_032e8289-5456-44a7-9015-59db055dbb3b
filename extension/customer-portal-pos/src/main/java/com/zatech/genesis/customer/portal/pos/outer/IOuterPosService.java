/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.genesis.customer.portal.pos.outer.fallback.OuterPosServiceFallbackFactory;
import com.zatech.genesis.customer.portal.pos.outer.request.PosRegisterCaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.PolicyPosItemResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosCaseCalculationResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosDocumentConfigResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosItemConfigResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosRegisterCaseResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosValidateCaseResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: weizhen.kong
 */
@FeignClient(name = "zatech-pos", url = "${genesis-feign-pos}", fallbackFactory = OuterPosServiceFallbackFactory.class)
public interface IOuterPosService {

    @Operation(summary = "Query policy POS items ")
    @GetMapping(value = {"/api/v2/policies/{policy-no}/pos-items"})
    List<PolicyPosItemResponse> listPosItems(@Parameter(name = "Policy No.") @PathVariable("policy-no") String policyNo);

    @Operation(summary = "Query pos item configuration details")
    @GetMapping("/api/v2/configurations")
    List<PosItemConfigResponse> listConfigurations(@Parameter(description = "Transaction Type.") @RequestParam("transType") List<TransTypeEnum> transType,
                                                   @Parameter(description = "Good ID.") @RequestParam(required = false, value = "goodsId") Long goodsId,
                                                   @Parameter(description = "Plan ID.") @RequestParam(required = false, value = "planId") Long planId);

    @Operation(summary = "Validate case whether can be apply")
    @PostMapping(value = "/api/v2/action/validate-application")
    PosValidateCaseResponse validateCase(@RequestBody PosRegisterCaseRequest validationRequest);

    @Operation(summary = "Create case")
    @PostMapping(value = {"/api/v2/cases"})
    PosRegisterCaseResponse applyCase(@RequestBody PosRegisterCaseRequest posCaseRequest);

    @GetMapping("/api/v2/query/document-configurations")
    PosDocumentConfigResponse getConfigDocuments(@Parameter(description = "Item Codes.") @RequestParam("itemCodes") List<String> itemCodes);

    @PostMapping("/api/v2/case/calculation")
    PosCaseCalculationResponse calculateCase(@RequestBody PosRegisterCaseRequest calculationRequest);
}