/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.document;

import com.zatech.genesis.customer.portal.pos.dataprovider.document.param.DocumentConfigParam;
import com.zatech.genesis.customer.portal.pos.dataprovider.document.result.DocumentConfigResult;
import com.zatech.genesis.customer.portal.pos.outer.IOuterMetadataService;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPosService;
import com.zatech.genesis.customer.portal.pos.outer.response.PosDocumentConfigResponse;
import com.zatech.genesis.metadata.api.bizdict.request.QueryBizDictTenantRequest;
import com.zatech.genesis.metadata.api.bizdict.response.BizDictTenantResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProviderKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.UIModelDataProvider;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;

/**
 * @Author: weizhen.kong
 */
@Slf4j
@UIModelDataProvider(
        name = "documents",
        productCategories = {FreeMartProductCategoryEnum.ALL},
        kind = DataProviderKind.data,
        desc = "获取保全项配置的document type")
@AllArgsConstructor
public class PosDocumentConfigDataProvider implements IDataProvider<DocumentConfigParam, DocumentConfigResult> {

    private final IOuterPosService outerPosService;

    private final IOuterMetadataService outerMetadataService;

    private static final String DOCUMENT_TYPE = "attachmentDocumentType";

    @Override
    public DocumentConfigResult provide(DocumentConfigParam uiModel, DataProvideContext dataProvideContext) {
        PosDocumentConfigResponse configDocuments = outerPosService.getConfigDocuments(Arrays.asList(uiModel.getPosItemType().name()));
        DocumentConfigResult configResult = new DocumentConfigResult();
        if (configDocuments != null && CollectionUtils.isNotEmpty(configDocuments.getConfigDocuments())) {
            QueryBizDictTenantRequest dictTenantRequest = new QueryBizDictTenantRequest();
            dictTenantRequest.setDictKeys(Arrays.asList(DOCUMENT_TYPE));
            List<BizDictTenantResponse> documentTypeEnums = outerMetadataService.queryBizDictTenant(dictTenantRequest);

            configResult.setConfigDocuments(configDocuments.getConfigDocuments().stream()
                    .collect(Collectors.toMap(PosDocumentConfigResponse.ConfigDocument::getDocumentType, obj -> obj, (existing, replacement) -> existing))
                    .values().stream()
                    .map(config -> new DocumentConfigResult.ConfigDocument(convertDocumentType(config.getDocumentType(), documentTypeEnums), config.getMandatory())).collect(Collectors.toList()));
        }
        return configResult;
    }

    private String convertDocumentType(String documentType, List<BizDictTenantResponse> documentTypeEnums) {
        if (CollectionUtils.isEmpty(documentTypeEnums)) {
            return documentType;
        }
        return documentTypeEnums.stream()
                .filter(dict -> documentType.equalsIgnoreCase(dict.getDictValue()))
                .findFirst()
                .map(BizDictTenantResponse::getDictValueName)
                .orElse(documentType);
    }
}