/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.payment.result;

/**
 * @Author: weizhen.kong
 */
public enum TransStatusEnum {

    /**
     * 支付成功
     */
    SUCCESS,
    /**
     * 支付失败
     */
    FAILURE,
    /**
     * 重复支付
     */
    DUPLICATE_PAYMENT,
    /**
     * 支付中
     */
    PAYING,
    /**
     * 下一步
     * 跳转redirectUrl
     */
    NEXT_PROCESS,
    /**
     * 未知,未明确的Code
     */
    UNKNOWN

}