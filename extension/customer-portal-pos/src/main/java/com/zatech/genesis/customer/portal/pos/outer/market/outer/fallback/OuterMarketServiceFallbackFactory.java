/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.market.outer.fallback;

import com.zatech.genesis.customer.portal.pos.outer.market.MarketErrorCodes;
import com.zatech.genesis.customer.portal.pos.outer.market.outer.OuterMarketService;
import com.zatech.genesis.customer.portal.pos.outer.market.request.GreenCardRequest;
import com.zatech.genesis.customer.portal.pos.outer.market.response.GoodsBasicInfoResp;
import com.zatech.genesis.customer.portal.pos.outer.market.response.GreenCardResponse;
import com.zatech.genesis.market.api.calculate.request.sapremium.SaPremiumCalcInitialPeriodRequest;
import com.zatech.genesis.market.api.calculate.response.sapremium.CalcSaPremiumMultiPeriodResponse;
import com.zatech.genesis.market.api.structure.request.PackageApplicationElementsEnumRequest;
import com.zatech.genesis.market.api.structure.request.QueryEffectiveDateRequest;
import com.zatech.genesis.market.api.structure.request.QueryExpiryDateRequest;
import com.zatech.genesis.market.api.structure.request.QueryGoodsRelatingListRequest;
import com.zatech.genesis.market.api.structure.request.QueryGoodsRelatingRequest;
import com.zatech.genesis.market.api.structure.response.GoodsRelatingResponse;
import com.zatech.genesis.market.api.structure.response.PackageApplicationElementsEnumResponse;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;
import com.zatech.genesis.portal.toolbox.exception.errorcode.SystemErrorCodes;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;
import com.zatech.genesis.portal.toolbox.exception.mapping.ISourceErrorMapping;
import com.zatech.genesis.portal.toolbox.exception.mapping.ISourceErrorMappingDefault;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;

import java.util.Date;
import java.util.List;

import jakarta.validation.constraints.NotNull;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024/1/24 10:45
 **/
@Component("fm-outerMarketServiceFallbackFactory")
public class OuterMarketServiceFallbackFactory extends AbstractFallbackFactory<OuterMarketService> implements ISourceErrorMapping, ISourceErrorMappingDefault {

    @Override
    public OuterMarketService create(Throwable cause) {
        return new OuterMarketService() {

            @Override
            public Date queryExpiryDate(QueryExpiryDateRequest request) {
                throw outerServiceException(cause, StaticJsonParser.toJsonString(request));
            }

            @Override
            public Date queryEffectiveDate(QueryEffectiveDateRequest request) {
                throw outerServiceException(cause, StaticJsonParser.toJsonString(request));
            }

            @Override
            public GoodsRelatingResponse queryGoodsRelating(@NotNull QueryGoodsRelatingRequest request) {
                throw outerServiceException(cause, StaticJsonParser.toJsonString(request));
            }

            @Override
            public List<GoodsBasicInfoResp> queryGoodsRelatingList(QueryGoodsRelatingListRequest requestDTO) {
                throw outerServiceException(cause, StaticJsonParser.toJsonString(requestDTO));
            }

            @Override
            public PackageApplicationElementsEnumResponse queryPackageApplicationElementEnumItem(PackageApplicationElementsEnumRequest request) {
                throw outerServiceException(cause, StaticJsonParser.toJsonString(request));
            }

            @Override
            public List<CalcSaPremiumMultiPeriodResponse> batchCalculateSaPremium(List<SaPremiumCalcInitialPeriodRequest> requests) {
                throw outerServiceException(cause, StaticJsonParser.toJsonString(requests));
            }

            @Override
            public GreenCardResponse calculateGreenCardFee(List<GreenCardRequest> request) {
                throw outerServiceException(cause, StaticJsonParser.toJsonString(request));
            }
        };
    }

    @Override
    public Class<? extends IErrorCode>[] mappingErrorCodes() {
        return new Class[] {MarketErrorCodes.class};
    }

    @Override
    public IErrorCode defaultErrorCode() {
        return SystemErrorCodes.internal_server_error;
    }
}
