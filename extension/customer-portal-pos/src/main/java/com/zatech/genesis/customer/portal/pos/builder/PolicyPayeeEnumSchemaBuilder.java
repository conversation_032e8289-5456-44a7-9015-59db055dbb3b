package com.zatech.genesis.customer.portal.pos.builder;

import com.google.common.collect.Lists;
import com.zatech.gaia.resource.components.enums.bcp.PayMethodEnum;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.PhoneTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.gaia.resource.graphene.customer.AccountTypeEnum;
import com.zatech.genesis.customer.portal.pos.builder.param.PolicyPayeeEnumSchemaBuilderParam;
import com.zatech.genesis.customer.portal.pos.service.MetadataService;
import com.zatech.genesis.customer.portal.pos.common.CommonConstant;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.RichSchema;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.ISchemaNodeBuilder;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildMetaInfo;
import com.zatech.genesis.portal.toolbox.share.model.EnumItem;

import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.media.StringSchema;

import java.util.Arrays;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Slf4j
@Component
public class PolicyPayeeEnumSchemaBuilder implements ISchemaNodeBuilder<PolicyPayeeEnumSchemaBuilderParam> {

    private static final String ACCOUNT_TYPE = "accountType";

    private static final String PAY_METHOD = "payMethod";

    private static final String PHONE_TYPE = "phoneType";

    private static final String PARTY_TYPE = "partyType";

    private static final String CERTI_TYPE = "certiType";

    private static final List<String> ENUM_KEYS = Lists.newArrayList(ACCOUNT_TYPE, PARTY_TYPE, PAY_METHOD, PHONE_TYPE, CERTI_TYPE);

    @Resource
    private MetadataService metadataService;

    @Override
    public PolicyPayeeEnumSchemaBuilderParam buildParam(SchemaNodeBuildContext schemaNodeBuildContext, SchemaNodeBuildMetaInfo schemaNodeBuildMetaInfo) {
        final var keyValuesForMap = metadataService.loadItems(ENUM_KEYS);
        final var fieldName = schemaNodeBuildContext.getFieldMeta().getField().getName();
        final var param = new PolicyPayeeEnumSchemaBuilderParam();
        param.setCode(fieldName);
        switch (fieldName) {
            case ACCOUNT_TYPE -> setEnumItemsForField(AccountTypeEnum.values(), param, keyValuesForMap.get(ACCOUNT_TYPE));
            case PAY_METHOD -> setEnumItemsForField(PayMethodEnum.values(), param, keyValuesForMap.get(PAY_METHOD));
            case PHONE_TYPE -> setEnumItemsForField(PhoneTypeEnum.values(), param, keyValuesForMap.get(PHONE_TYPE));
            case PARTY_TYPE -> setEnumItemsForField(PartyTypeEnum.values(), param, keyValuesForMap.get(PARTY_TYPE));
            case CERTI_TYPE -> setEnumItemsForField(CertiTypeEnum.values(), param, keyValuesForMap.get(CERTI_TYPE));
            default -> log.info("Not found fieldName:{}", fieldName);
        }
        return param;
    }

    private void setEnumItemsForField(Enum<?>[] values, PolicyPayeeEnumSchemaBuilderParam param, List<EnumItem> items) {
        if (CollectionUtils.isNotEmpty(items)) {
            param.setEnumItems(items);
            return;
        }
        final var enumItems = Arrays.stream(values)
                .map(e -> new EnumItem(e.name(), e.name()))
                .toList();
        param.setEnumItems(enumItems);
    }

    @Override
    public Schema buildSchema(Schema originalSchema, PolicyPayeeEnumSchemaBuilderParam policyPayeeEnumSchemaBuilderParam, SchemaNodeBuildContext schemaNodeBuildContext, SchemaNodeBuildMetaInfo schemaNodeBuildMetaInfo) {
        if (StringUtils.isEmpty(policyPayeeEnumSchemaBuilderParam.getCode())) {
            return originalSchema;
        }
        final var schema = new StringSchema();
        schema.addExtension(CommonConstant.SCHEMA_ENUM_NODE, policyPayeeEnumSchemaBuilderParam.getEnumItems());
        schema.addExtension(CommonConstant.SCHEMA_EDITABLE, false);
        schema.setReadOnly(true);
        return new RichSchema.RichSchema(schema).asEnum();
    }
}
