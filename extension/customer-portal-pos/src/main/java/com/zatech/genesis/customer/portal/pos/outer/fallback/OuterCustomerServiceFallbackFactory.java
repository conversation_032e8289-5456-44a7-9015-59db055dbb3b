/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.fallback;

import com.zatech.gaia.resource.customer.scenario.share.dto.base.CustOptionEnum;
import com.zatech.genesis.customer.portal.pos.outer.IOuterCustomerService;
import com.zatech.genesis.customer.portal.pos.outer.request.PartyCustomerCreateRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.PartyCustomerCreateResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PolicyCustomerResponse;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;

import java.util.List;

import org.springframework.stereotype.Component;

/**
 * @Author: weizhen.kong
 */
@Component
public class OuterCustomerServiceFallbackFactory extends AbstractFallbackFactory<IOuterCustomerService> {

    @Override
    public IOuterCustomerService create(Throwable cause) {
        return new IOuterCustomerService() {
            @Override
            public PolicyCustomerResponse queryCustomer(Long customerId, List<CustOptionEnum> options) {
                throw outerServiceException(cause);
            }

            @Override
            public PartyCustomerCreateResponse createPartyCustomer(PartyCustomerCreateRequest posPayerInfo) {
                throw outerServiceException(cause);
            }
        };
    }
}