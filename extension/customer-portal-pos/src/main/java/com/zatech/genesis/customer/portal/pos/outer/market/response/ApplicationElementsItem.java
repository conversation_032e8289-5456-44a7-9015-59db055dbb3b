/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.market.response;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;

import java.util.List;

import lombok.experimental.Accessors;

@Accessors(chain = true)
public class ApplicationElementsItem {

    private String code;

    private YesNoEnum isUpdatableWhenPos;

    private YesNoEnum isUpdatableWhenUw;

    private List<EnumItem> items;

    public String getCode() {
        return code;
    }

    public ApplicationElementsItem setCode(String code) {
        this.code = code;
        return this;
    }

    public YesNoEnum getIsUpdatableWhenPos() {
        return isUpdatableWhenPos;
    }

    public ApplicationElementsItem setIsUpdatableWhenPos(YesNoEnum isUpdatableWhenPos) {
        this.isUpdatableWhenPos = isUpdatableWhenPos;
        return this;
    }

    public YesNoEnum getIsUpdatableWhenUw() {
        return isUpdatableWhenUw;
    }

    public ApplicationElementsItem setIsUpdatableWhenUw(YesNoEnum isUpdatableWhenUw) {
        this.isUpdatableWhenUw = isUpdatableWhenUw;
        return this;
    }

    public List<EnumItem> getItems() {
        return items;
    }

    public ApplicationElementsItem setItems(List<EnumItem> items) {
        this.items = items;
        return this;
    }

}
