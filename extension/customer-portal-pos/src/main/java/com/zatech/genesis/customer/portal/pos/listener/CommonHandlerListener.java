/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.listener;

import com.zatech.genesis.customer.portal.pos.event.AbstractEvent;
import com.zatech.genesis.customer.portal.pos.listener.handler.AbstractListenerHandler;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import lombok.AllArgsConstructor;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

import static com.za.cqrs.util.Functions.doIf;

/**
 * @Author: weizhen.kong
 */
@Component
@AllArgsConstructor
public class CommonHandlerListener implements ApplicationListener<AbstractEvent> {

    private final List<AbstractListenerHandler> listenerHandlers;

    private static final Map<Type, List<AbstractListenerHandler>> HANDLERS = new ConcurrentHashMap();

    @PostConstruct
    public void setUp() {
        doIf(CollectionUtils.isNotEmpty(listenerHandlers), () ->
                listenerHandlers.stream().forEach(handler -> {
                    Type genericSuperclass = handler.getClass().getGenericSuperclass();
                    if (genericSuperclass instanceof ParameterizedType) {
                        Type actualType = ((ParameterizedType) genericSuperclass).getActualTypeArguments()[0];
                        if (HANDLERS.containsKey(actualType)) {
                            HANDLERS.get(actualType).add(handler);
                        } else {
                            LinkedList<AbstractListenerHandler> handlerList = new LinkedList<>();
                            handlerList.add(handler);
                            HANDLERS.put(actualType, handlerList);
                        }
                    }
                }));
    }

    @Override
    public void onApplicationEvent(AbstractEvent event) {
        HANDLERS.get(event.getClass()).stream().forEach(listenerHandler -> listenerHandler.handle(event));
    }

}