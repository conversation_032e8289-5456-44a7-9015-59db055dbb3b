package com.zatech.genesis.customer.portal.pos.dataprovider.payment;

import com.zatech.gaia.resource.components.enums.paymentgateway.PayChannelEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayMethodEnum;
import com.zatech.genesis.customer.portal.pos.dataprovider.payment.request.PaymentConfigRequest;
import com.zatech.genesis.customer.portal.pos.dataprovider.payment.result.PaymentChannelResult;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPaymentService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProviderKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvideMockup;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.UIModelDataProvider;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;

import java.util.Arrays;

import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.Resource;

@Slf4j
@UIModelDataProvider(
        name = "paymentChannel",
        productCategories = {FreeMartProductCategoryEnum.ALL},
        kind = DataProviderKind.data,
        desc = "获取支付渠道信息")
public class PaymentChannelDataProvider implements IDataProvider<PaymentConfigRequest, PaymentChannelResult>, IDataProvideMockup<PaymentConfigRequest, PaymentChannelResult> {

    @Resource
    private IOuterPaymentService paymentService;

    @Override
    public PaymentChannelResult provide(PaymentConfigRequest paymentRequest, DataProvideContext dataProvideContext) {
        if (dataProvideContext.getParams().containsKey("channelCode")) {
            return new PaymentChannelResult().setResults(paymentService.getValidPayChannelList(String.valueOf(dataProvideContext.getParams().get("channelCode"))).stream()
                    .map(PaymentChannelResult.PaymentChannel::from).toList());
        }
        return new PaymentChannelResult();
    }

    @Override
    public boolean shouldMock(PaymentConfigRequest paymentConfigRequest, DataProvideContext dataProvideContext) {
        return Boolean.TRUE.equals(dataProvideContext.getParams().get("mock"));
    }

    @Override
    public PaymentChannelResult mock(PaymentConfigRequest paymentConfigRequest, DataProvideContext dataProvideContext) {
        PaymentChannelResult mock = new PaymentChannelResult();
        PaymentChannelResult.PaymentChannel paymentChannel = new PaymentChannelResult.PaymentChannel();
        paymentChannel.setPayChannel(PayChannelEnum.STRIPE);
        paymentChannel.setPayMethodList(Arrays.asList(PayMethodEnum.CARD));
        mock.setResults(Arrays.asList(paymentChannel));
        return mock;
    }
}
