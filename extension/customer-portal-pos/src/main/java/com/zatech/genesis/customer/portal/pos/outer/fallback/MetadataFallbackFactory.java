/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.fallback;

import com.zatech.genesis.customer.portal.pos.outer.IOuterMetadataService;
import com.zatech.genesis.customer.portal.pos.outer.request.MetadataDictI18nRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.MetadataDictI18nResponse;
import com.zatech.genesis.metadata.api.bizdict.request.QueryBizDictTenantRequest;
import com.zatech.genesis.metadata.api.bizdict.response.BizDictTenantResponse;
import com.zatech.genesis.metadata.api.uniquekey.request.SchemaDefUniqueKeyQueryRequest;
import com.zatech.genesis.metadata.api.uniquekey.response.SchemaDefUniqueKeyResponse;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;
import com.zatech.octopus.common.dao.Page;

import java.util.List;

import org.springframework.stereotype.Component;

/**
 * @Author: weizhen.kong
 */
@Component
public class MetadataFallbackFactory extends AbstractFallbackFactory<IOuterMetadataService> {

    @Override
    public IOuterMetadataService create(Throwable cause) {
        return new IOuterMetadataService() {
            @Override
            public Page<MetadataDictI18nResponse> queryTenantBizDictItemsPage(Page<MetadataDictI18nRequest> request) {
                throw outerServiceException(cause, String.format("dickKey: %s", request.getCondition().getEntryBizDictKey()));
            }

            @Override
            public List<BizDictTenantResponse> queryBizDictTenant(QueryBizDictTenantRequest request) {
                throw outerServiceException(cause, String.format("dickKey: %s", request.getDictKeys()));
            }

            @Override
            public List<SchemaDefUniqueKeyResponse> querySchemaDefUniqueKey(SchemaDefUniqueKeyQueryRequest schemaDefUniqueKeyQueryRequest) {
                throw outerServiceException(cause);
            }
        };
    }

    @Override
    public String outerServiceKey() {
        return "metadata";
    }
}