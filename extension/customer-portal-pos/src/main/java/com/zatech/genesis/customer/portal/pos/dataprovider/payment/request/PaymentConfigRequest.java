package com.zatech.genesis.customer.portal.pos.dataprovider.payment.request;

import com.zatech.genesis.customer.portal.biz.common.uimodel.BaseModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModel;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PaymentConfigRequest extends BaseModel implements IUIModel {

    private String policyNo;

    private String channelCode;
}
