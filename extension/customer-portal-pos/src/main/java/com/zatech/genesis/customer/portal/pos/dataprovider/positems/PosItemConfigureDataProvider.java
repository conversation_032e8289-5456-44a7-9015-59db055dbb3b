/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.positems;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyProductStatusChangeCauseEnum;
import com.zatech.gaia.resource.components.enums.schema.CustomerTypeEnum;
import com.zatech.gaia.resource.graphene.product.CancellationTypeEnum;
import com.zatech.genesis.customer.portal.pos.dataprovider.positems.mock.PosItemConfigureMockUtils;
import com.zatech.genesis.customer.portal.pos.dataprovider.positems.result.PosItemConfigureResult;
import com.zatech.genesis.customer.portal.pos.event.PolicyCustomerLoadEvent;
import com.zatech.genesis.customer.portal.pos.exception.PosPortalErrorCode;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPosService;
import com.zatech.genesis.customer.portal.pos.outer.OuterMarketAdapter;
import com.zatech.genesis.customer.portal.pos.outer.market.response.ApplicationElementItemsResponse;
import com.zatech.genesis.customer.portal.pos.outer.market.response.ApplicationElementsItem;
import com.zatech.genesis.customer.portal.pos.outer.response.PosItemConfigResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PosItemConfigResponse.PosItemModule;
import com.zatech.genesis.customer.portal.pos.template.IndividualCustomerElement;
import com.zatech.genesis.customer.portal.pos.template.IndividualCustomerElement.AddressTemplate;
import com.zatech.genesis.customer.portal.pos.template.IndividualCustomerElement.EmailElement;
import com.zatech.genesis.customer.portal.pos.template.IndividualCustomerElement.PhoneTemplate;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate.PolicyHolder;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate.PolicyInsured;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate.PosApplicationInfo;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProviderKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvideMockup;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.UIModelDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.IUIModelSchemaBuilder;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.IUIModelSchemaBuilderAware;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;
import com.zatech.genesis.portal.toolbox.share.model.EnumItem;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationContext;

import jakarta.annotation.Resource;

import static com.za.cqrs.util.Functions.doIf;
import static com.zatech.gaia.resource.components.enums.common.TransTypeEnum.FREELOOKSURRENDER;
import static com.zatech.gaia.resource.graphene.posonline.PosTransEffectiveDateEnum.POLICY_EFFECTIVE_DATE;
import static com.zatech.gaia.resource.graphene.posonline.PosTransEffectiveDateEnum.POS_IMMEDIATELY_DATE;
import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.APPLICATION_ELEMENT_CONFIG;
import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.HOLDER_ITEMS_ROLE;
import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.INSURED_ITEMS_ROLE;
import static com.zatech.genesis.customer.portal.pos.exception.PosPortalErrorCode.POS_PORTAL_APPLICATION_ELEMENT_CONFIG_EMPTY;
import static com.zatech.genesis.customer.portal.pos.util.UiModelReflectUtil.convertObjectFromPosConfig;
import static com.zatech.genesis.customer.portal.pos.util.UiModelReflectUtil.convertOriginalUiModel;
import static com.zatech.genesis.customer.portal.pos.util.UiModelReflectUtil.fetchRoleItems;

/**
 * @Author: weizhen.kong
 */
@Slf4j
@UIModelDataProvider(
        name = "posItemConfigures",
        productCategories = {FreeMartProductCategoryEnum.ALL},
        kind = DataProviderKind.data,
        desc = "根据goodsId,planId,itemType获取保全项配置")
public class PosItemConfigureDataProvider implements IDataProvider<PosDataEntryTemplate, PosItemConfigureResult>, IDataProvideMockup<PosDataEntryTemplate, PosItemConfigureResult>, IUIModelSchemaBuilderAware {

    private static final String POS_REASON_CODE = "posReason";

    private static final String SCHEMA_POS_CODE = "schemaPOS";

    private static final String EFFECTIVE_DATE_CODE = "posEffectiveDateType";

    private static final String SIGN_DATE_CODE = "signDateModule";

    private static final String REQUEST_DATE_CODE = "requestDateModule";

    private static final String CANCELLATION_CODE = "cancellationReason";

    private static final String CANCELLATION_TYPE_CODE = "cancellationType";

    private IUIModelSchemaBuilder uiModelSchemaBuilder;

    @Resource
    private IOuterPosService outerPosService;

    @Resource
    private OuterMarketAdapter outerMarketAdapter;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public boolean shouldMock(PosDataEntryTemplate uiModel, DataProvideContext dataProvideContext) {
        return PosItemConfigureMockUtils.shouldMock(uiModel.getPolicyNo());
    }

    @Override
    public PosItemConfigureResult mock(PosDataEntryTemplate uiModel, DataProvideContext dataProvideContext) {
        return PosItemConfigureMockUtils.posItemConfigureResult();
    }

    @Override
    public PosItemConfigureResult provide(PosDataEntryTemplate uiModel, DataProvideContext dataProvideContext) {
        TransTypeEnum posItem = uiModel.getPosItemType();
        LinkedList<TransTypeEnum> transTypeEnums = new LinkedList<>();
        transTypeEnums.add(posItem);
        doIf(uiModel.getPolicyHolder() != null && Boolean.TRUE.equals(uiModel.getPolicyHolder().getCascadeUpdateInsured()), () -> transTypeEnums.add(TransTypeEnum.POS_INSURED_INFO_CHANGES));

        List<PosItemConfigResponse> posItemConfigResponses = outerPosService.listConfigurations(transTypeEnums, uiModel.getGoodsId(), uiModel.getGoodsPlanId());
        doIf(CollectionUtils.isEmpty(posItemConfigResponses), () -> {
            throw CommonException.byError(PosPortalErrorCode.POS_PORTAL_ITEM_CONFIGURE_NOT_FOUND);
        });

        JsonMap jsonMap = new JsonMap();
        //更新uiModel数据之后，uiModel会回显所有的data数据，没法再根据config进行过滤，所以构建schema使用新的uiModel实例
        PosDataEntryTemplate schemaModel = new PosDataEntryTemplate();
        schemaModel.setGoodsId(uiModel.getGoodsId());
        schemaModel.setGoodsPlanId(uiModel.getGoodsPlanId());
        schemaModel.setPosItemType(posItem);

        // 以uiModel最外层的posItem为主
        processPosApplication(schemaModel, posItemConfigResponses.stream().filter(config -> config.getItemCode().equals(posItem.name())).collect(Collectors.toList()), jsonMap, posItem);

        transTypeEnums.forEach(item -> convertTransaction(schemaModel, posItemConfigResponses.stream().filter(config -> config.getItemCode().equals(item.name())).collect(Collectors.toList()), jsonMap, item));

        publishPolicyCustomerLoadEvent(dataProvideContext.getOrder().getOrderNo(), dataProvideContext.getOrder().getModuleId(), posItem, uiModel);
        return new PosItemConfigureResult(uiModelSchemaBuilder.buildSchema(schemaModel, jsonMap));
    }

    private void publishPolicyCustomerLoadEvent(String orderNo, Long moduleId, TransTypeEnum posItemType, PosDataEntryTemplate uiModel) {
        if (TransTypeEnum.POS_HOLDER_INFO_CHANGES.equals(uiModel.getPosItemType()) || TransTypeEnum.POS_INSURED_INFO_CHANGES.equals(uiModel.getPosItemType())) {
            applicationContext.publishEvent(new PolicyCustomerLoadEvent(uiModel.getPolicyNo(), orderNo, moduleId, posItemType, convertOriginalUiModel(uiModel)));
        }
    }

    private void convertTransaction(PosDataEntryTemplate uiModel, List<PosItemConfigResponse> posItemConfigResponses, JsonMap jsonMap, TransTypeEnum itemType) {
        List<PosItemModule> individualConfig = posItemConfigResponses.stream().findFirst().map(configResponse -> configResponse.getPosItemModules().stream().filter(config -> !CustomerTypeEnum.COMPANY.equals(config.getCustomerType())).collect(Collectors.toList())).orElse(Collections.EMPTY_LIST);

        AtomicReference<IndividualCustomerElement> individual = new AtomicReference<>();
        doIf(TransTypeEnum.POS_HOLDER_INFO_CHANGES.equals(itemType), () -> {
            individual.set(new PolicyHolder());
            uiModel.setPolicyHolder((PolicyHolder) individual.get());
            jsonMap.put(APPLICATION_ELEMENT_CONFIG, getApplicationElementsItems(uiModel.getGoodsId(), uiModel.getGoodsPlanId(), HOLDER_ITEMS_ROLE));
        });
        doIf(TransTypeEnum.POS_INSURED_INFO_CHANGES.equals(itemType), () -> {
            individual.set(new PolicyInsured());
            uiModel.setPolicyInsured((PolicyInsured) individual.get());
            jsonMap.put(APPLICATION_ELEMENT_CONFIG, getApplicationElementsItems(uiModel.getGoodsId(), uiModel.getGoodsPlanId(), INSURED_ITEMS_ROLE));
        });

        individualConfig.stream().filter(config -> config.getCustomerType() != null).forEach(config -> {
            switch (config.getCustomerSubType()) {
                case BASIC_INFO ->
                        convertObjectFromPosConfig(individual.get(), config.getPosItemModuleProperties(), jsonMap);
                case ADDRESS -> {
                    AddressTemplate addressTemplate = new AddressTemplate();
                    individual.get().setAddresses(Arrays.asList(addressTemplate));
                    convertObjectFromPosConfig(addressTemplate, config.getPosItemModuleProperties(), jsonMap);
                }
                case PHONE -> {
                    PhoneTemplate phoneTemplate = new PhoneTemplate();
                    if (individual.get() instanceof PolicyHolder policyHolder) {
                        policyHolder.setPhones(Arrays.asList(phoneTemplate));
                    } else if (individual.get() instanceof PolicyInsured policyInsured) {
                        policyInsured.setPhones(Arrays.asList(phoneTemplate));
                    }
                    convertObjectFromPosConfig(phoneTemplate, config.getPosItemModuleProperties(), jsonMap);
                }
                case EMAIL -> {
                    EmailElement email = new EmailElement();
                    individual.get().setEmail(email);
                    convertObjectFromPosConfig(email, config.getPosItemModuleProperties(), jsonMap);
                }
                default ->
                        log.warn("Can find customer sub type to process: {} policyNo: {}, itemType: {}", config.getCustomerType(), uiModel.getPolicyNo(), uiModel.getPosItemType());
            }
        });
    }

    private static List<PosItemModule> processPosApplication(PosDataEntryTemplate uiModel, List<PosItemConfigResponse> posItemConfigResponses, JsonMap jsonMap, TransTypeEnum itemType) {
        PosApplicationInfo posApplicationInfo = new PosApplicationInfo();
        uiModel.setPosApplicationInfo(posApplicationInfo);
        List<PosItemModule> individualConfig = posItemConfigResponses.stream().findFirst().map(configResponse -> configResponse.getPosItemModules().stream().filter(config -> !CustomerTypeEnum.COMPANY.equals(config.getCustomerType())).collect(Collectors.toList())).orElse(Collections.EMPTY_LIST);

        individualConfig.stream().filter(config -> config.getCustomerType() == null && CollectionUtils.isNotEmpty(config.getPosItemModuleProperties()))
                .forEach(config -> {
                    switch (config.getModuleCode()) {
                        case POS_REASON_CODE -> {
                            jsonMap.put(POS_REASON_CODE, config.getPosItemModuleProperties().stream().map(enumConfig -> new EnumItem(enumConfig.getPropertyCode(), enumConfig.getPropertyName())).collect(Collectors.toList()));
                            posApplicationInfo.setReasonCode("reasonCode");
                            posApplicationInfo.setReason("input");
                            posApplicationInfo.setLifeEventDate(new Date());
                        }
                        case EFFECTIVE_DATE_CODE -> {
                            if (FREELOOKSURRENDER.equals(itemType)) {
                                posApplicationInfo.setEffectiveDateType(POLICY_EFFECTIVE_DATE);
                                jsonMap.put(EFFECTIVE_DATE_CODE, Arrays.asList(new EnumItem(POLICY_EFFECTIVE_DATE.getCode(), POLICY_EFFECTIVE_DATE.getDesc())));
                            } else {
                                posApplicationInfo.setEffectiveDateType(POS_IMMEDIATELY_DATE);
                                jsonMap.put(EFFECTIVE_DATE_CODE, Arrays.asList(new EnumItem(POS_IMMEDIATELY_DATE.getCode(), POS_IMMEDIATELY_DATE.getDesc())));
                            }
                        }
                        case SIGN_DATE_CODE -> posApplicationInfo.setSignDate(new Date());
                        case REQUEST_DATE_CODE -> posApplicationInfo.setRequestDate(new Date());
                        case CANCELLATION_CODE -> {
                            jsonMap.put(CANCELLATION_CODE, convertEnumParam(config));
                            posApplicationInfo.setCancellationReason(PolicyProductStatusChangeCauseEnum.NO_REASON);
                            posApplicationInfo.setReason("input");
                        }
                        case CANCELLATION_TYPE_CODE -> {
                            jsonMap.put(CANCELLATION_TYPE_CODE, convertEnumParam(config));
                            posApplicationInfo.setCancellationType(CancellationTypeEnum.CANCEL);
                        }
                        default ->
                                log.warn("Pos item configure module code not register, transType: {}, moduleCode: {}", itemType, config.getModuleCode());
                    }
                });
        return individualConfig;
    }

    private List<ApplicationElementsItem> getApplicationElementsItems(Long goodsId, Long planId, String itemsRole) {
        ApplicationElementItemsResponse applicationElementItems = outerMarketAdapter.queryApplicationElements(goodsId, planId);
        List<ApplicationElementsItem> roleItems = fetchRoleItems(applicationElementItems, itemsRole);
        doIf(CollectionUtils.isEmpty(roleItems), () -> {
            throw CommonException.byError(POS_PORTAL_APPLICATION_ELEMENT_CONFIG_EMPTY);
        });
        return roleItems;
    }

    private static List<EnumItem> convertEnumParam(PosItemModule config) {
        return config.getPosItemModuleProperties().stream().map(enumConfig -> new EnumItem(enumConfig.getEnumName(), enumConfig.getPropertyName())).collect(Collectors.toList());
    }

    @Override
    public void setUIModelSchemaBuilder(IUIModelSchemaBuilder iuiModelSchemaBuilder) {
        this.uiModelSchemaBuilder = iuiModelSchemaBuilder;
    }
}