/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.exception;

import com.zatech.genesis.portal.toolbox.exception.StandardErrorCode;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: weizhen.kong
 */
@Slf4j
public enum PosPortalErrorCode implements IErrorCode {

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    POS_PORTAL_ITEM_CONFIGURE_NOT_FOUND,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.BusinessError$500)
    POLICY_NOT_FOUND,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.BusinessError$500)
    POS_SERVICE_UNAVAILABLE,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    POS_PORTAL_APPLICATION_ELEMENT_CONFIG_EMPTY,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.BusinessError$500)
    STEP_FILTER_NOT_IMPL;

    @Override
    public String getModuleName() {
        return "pp";
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}