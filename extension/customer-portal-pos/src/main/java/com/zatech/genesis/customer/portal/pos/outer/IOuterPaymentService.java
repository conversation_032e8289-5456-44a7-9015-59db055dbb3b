package com.zatech.genesis.customer.portal.pos.outer;

import com.zatech.genesis.customer.portal.pos.dataprovider.payment.request.PaymentRequest;
import com.zatech.genesis.customer.portal.pos.outer.fallback.OuterPaymentServiceFallbackFactory;
import com.zatech.genesis.payment.gateway.api.request.QueryPayOrderReq;
import com.zatech.genesis.payment.gateway.api.response.PayAccountResp;
import com.zatech.genesis.payment.gateway.api.response.PayOrderResp;
import com.zatech.octopus.common.dao.Page;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "zatech-payment-gateway", url = "${genesis-feign-payment-gateway}", fallbackFactory = OuterPaymentServiceFallbackFactory.class)
public interface IOuterPaymentService {

    @GetMapping(path = {"/api/config/validPayChannelList"})
    List<PayAccountResp> getValidPayChannelList(@RequestParam("channelCode") String channelCode);

    @PostMapping(value = "/api/payment/pay")
    PayOrderResp pay(@RequestBody PaymentRequest var1);

    @RequestMapping(value = {"/api/payOrder/{payOrderNo}/query"}, method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    PayOrderResp queryPayOrder(@PathVariable("payOrderNo") String payOrderNo);

    @PostMapping(value = "/api/payOrder/query")
    Page<com.zatech.genesis.customer.portal.integration.outer.openapi.payment.response.PayOrderResp> queryPayOrder(@RequestBody QueryPayOrderReq queryPayOrderReq);

}
