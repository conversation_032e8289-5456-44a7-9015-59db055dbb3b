/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer;

import com.zatech.genesis.customer.portal.pos.outer.fallback.OuterPolicyServiceFallbackFactory;
import com.zatech.genesis.policy.api.response.PolicyResponse;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: weizhen.kong
 */
@FeignClient(name = "zatech-inner-policy", url = "${genesis-feign-policy}", fallbackFactory = OuterPolicyServiceFallbackFactory.class)
public interface IOuterPolicyService {

    @GetMapping(value = {"/api/v2/policies/{policyNo}"})
    PolicyResponse queryPolicy(@PathVariable(value = "policyNo") String policyNo,
                               @RequestParam(value = "eventNo", required = false) String eventNo,
                               @RequestParam(value = "queryCustomer", required = false) Boolean queryCustomer);

}