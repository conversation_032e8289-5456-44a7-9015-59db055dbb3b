/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.file;

import com.zatech.genesis.customer.portal.pos.outer.file.fallback.IOuterFileServiceFallbackFactory;
import com.zatech.genesis.customer.portal.pos.outer.file.request.FileRequest;
import com.zatech.genesis.customer.portal.pos.outer.file.response.FileOperateDefine;
import com.zatech.octopus.module.web.dto.ResultBase;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "fm-file", url = "${genesis-feign-file}", fallbackFactory = IOuterFileServiceFallbackFactory.class)
public interface IOuterFileService {

    @PostMapping(value = "/file/operate", headers = "content-type=application/json")
    ResultBase<FileOperateDefine> operate(FileRequest fileRequest);

}
