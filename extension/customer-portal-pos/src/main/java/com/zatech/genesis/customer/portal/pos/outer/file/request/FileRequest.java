/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.file.request;

import com.zatech.gaia.resource.file.component.share.enums.common.FileSubCategoryEnum;
import com.zatech.gaia.resource.file.component.share.enums.file.FileOperateTypeEnum;
import com.zatech.gaia.resource.file.component.share.enums.file.FileSrcTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.Map;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(exclude = {"content"})
@NoArgsConstructor
public class FileRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 文件是否上传成功：1.待上传，2.上传成功，3.上传失败
     */
    private Integer fileStatus;

    /**
     * 关联对象，如 报案号
     */
    @Schema(name = "关联对象，如 报案号")
    private String srcObj;

    /**
     * 关联对象类型
     */
    @Schema(name = "关联对象类型")
    private FileSrcTypeEnum srcType;

    /**
     * 文件名字+文件后缀,如：身份证.jpg
     */
    @Schema(name = "文件名字+文件后缀,如：身份证.jpg")
    private String fileName;

    /**
     * 文件描述
     */
    @Schema(name = "文件描述")
    private String description;

    @Schema(name = "业务类型,替换FileCategoryEnum用于文件客制化路径操作")
    private String businessType;

    /**
     * 系统功能子分类
     */
    @Schema(name = "系统功能子分类")
    private FileSubCategoryEnum fileSubCategory;

    /**
     * 文件类别即文件大类下面的进一步分类，由各个调用系统自己控制,如：理赔的出险原因
     */
    @Schema(name = "文件类别即文件大类下面的进一步分类，由各个调用系统自己控制,如：理赔的出险原因")
    private Integer fileType;

    /**
     * 文件存储信息
     */
    @Schema(name = "文件存储信息")
    private byte[] content;

    //*********查询需要 begin************

    /**
     * 文件统一编码(对于文件系统全局唯一)
     */
    @Schema(name = "文件统一编码(对于文件系统全局唯一)")
    private String fileUniqueCode;

    /**
     * 操作类型
     */
    @Schema(name = "操作类型")
    private FileOperateTypeEnum fileOperateType;

    /**
     * 图片编号
     */
    @Schema(name = "imageNo")
    private String imageNo;

    //*********查询需要end************

    /**
     * ak-properties-mapping 文件存储区分存在多个桶
     */
    @Schema(name = "configId")
    private String configId;

    /**
     * 业务唯一键
     */
    @Schema(name = "businessUniqueId")
    private String businessUniqueId;

    /**
     * 请求头
     */
    @Schema(name = "contentMap")
    private Map<String, Object> contentMap;

    /**
     * 是否加密
     */
    @Schema(name = "是否加密")
    private Boolean isEncryption;

    @Schema(name = "临时url操作请求方式")
    private String httpMethod;

    @Schema(name = "文件内部路径")
    private String fileInsideUrl;

    @Schema(name = "文件零时url时效")
    private Date expiration;

    @Schema(name = "文件路径")
    private String filePath;

    @Schema(title = "上传文件时所附带的信息")
    private String additionalInfo;

    @Schema(title = "拷贝后的文件新路径")
    private String desFilePath;

}
