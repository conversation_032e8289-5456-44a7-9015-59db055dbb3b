/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.steps.stepfilter;

import com.zatech.gaia.resource.components.enums.bcp.PayFeeEnum;
import com.zatech.genesis.customer.portal.pos.dataprovider.steps.result.PosPageStep;
import com.zatech.genesis.customer.portal.pos.dataprovider.steps.result.PosPageStepEnum;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPosService;
import com.zatech.genesis.customer.portal.pos.outer.request.PosRegisterCaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.PosCaseCalculationResponse;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;

import lombok.AllArgsConstructor;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static com.zatech.genesis.customer.portal.pos.util.UiModelReflectUtil.convertUimodel2PosCase;

/**
 * @Author: weizhen.kong
 */
@Component
@Order(30)
@AllArgsConstructor
public class CollectionStepFilter extends AbstractPosStepFilter {

    private final IOuterPosService outerPosService;

    @Override
    public PosPageStep filter(PosDataEntryTemplate dataEntry) {
        if (!preCheck(dataEntry)) {
            return new PosPageStep(PosPageStepEnum.PAYMENT, Boolean.FALSE);
        }

        PosRegisterCaseRequest caseRequest = new PosRegisterCaseRequest();
        convertUimodel2PosCase(dataEntry, caseRequest);
        PosCaseCalculationResponse calculationResponse = outerPosService.calculateCase(caseRequest);
        return new PosPageStep(PosPageStepEnum.PAYMENT, calculationResponse != null && PayFeeEnum.PRESERVATION_RECEIVABLE.equals(calculationResponse.getPayFeeType()));
    }
}