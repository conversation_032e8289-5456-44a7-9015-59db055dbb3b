/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.builder;

import com.zatech.genesis.customer.portal.pos.builder.param.PosConfigureEnumBuilderParam;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.RichSchema;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.ISchemaNodeBuilder;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildMetaInfo;
import com.zatech.genesis.portal.toolbox.share.model.EnumItem;

import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.media.StringSchema;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.SCHEMA_ENUM_NODE;

/**
 * @Author: weizhen.kong
 */
@Component
public class PosApplicationConfigureEnumSchemaBuilder implements ISchemaNodeBuilder<PosConfigureEnumBuilderParam> {
    @Override
    public PosConfigureEnumBuilderParam buildParam(SchemaNodeBuildContext schemaNodeBuildContext, SchemaNodeBuildMetaInfo schemaNodeBuildMetaInfo) {
        PosConfigureEnumBuilderParam builderParam = new PosConfigureEnumBuilderParam();
        schemaNodeBuildContext.getBuilderParams().forEach((key, value) ->
                Optional.ofNullable(schemaNodeBuildContext.getTemplateParams().get(value)).ifPresent(posConfigure -> {
                    builderParam.setFileCode(key);
                    builderParam.setEnumItems((List<EnumItem>) posConfigure);
                }));
        return builderParam;
    }

    @Override
    public Schema buildSchema(Schema originalSchema, PosConfigureEnumBuilderParam param, SchemaNodeBuildContext schemaNodeBuildContext, SchemaNodeBuildMetaInfo schemaNodeBuildMetaInfo) {
        if (StringUtils.isEmpty(param.getFileCode())) {
            return originalSchema;
        }
        final var schema = new StringSchema();
        schema.addExtension(SCHEMA_ENUM_NODE, param.getEnumItems());
        return new RichSchema.RichSchema(schema).asEnum();
    }
}