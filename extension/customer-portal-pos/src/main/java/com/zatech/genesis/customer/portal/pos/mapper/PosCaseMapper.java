/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.mapper;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.genesis.customer.portal.pos.outer.request.PartyCustomerCreateRequest;
import com.zatech.genesis.customer.portal.pos.outer.request.PosCaseBaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.request.PosCaseBaseRequest.PosTransaction;
import com.zatech.genesis.customer.portal.pos.outer.response.CustomerCenterIndividualResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PolicyCustomerResponse;
import com.zatech.genesis.customer.portal.pos.outer.response.PolicyCustomerResponse.CustomerEmail;
import com.zatech.genesis.customer.portal.pos.template.AttachmentInfo;
import com.zatech.genesis.customer.portal.pos.template.IndividualCustomerElement;
import com.zatech.genesis.customer.portal.pos.template.IndividualCustomerElement.EmailElement;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate.PosApplicationInfo;
import com.zatech.octopus.framework.mapper.MapStructBaseMapper;
import com.zhongan.graphene.pos.online.api.common.customer.PosCustomerEmail;
import com.zhongan.graphene.pos.online.api.common.customer.PosCustomerPhone;
import com.zhongan.graphene.pos.online.api.common.pos.PosPayerInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: weizhen.kong
 */
@Mapper
public interface PosCaseMapper extends MapStructBaseMapper {

    PosCaseMapper MAPPER = Mappers.getMapper(PosCaseMapper.class);

    PosTransaction convertPosApplication(PosApplicationInfo posApplicationInfo, TransTypeEnum posTransType);

    PosCaseBaseRequest.PolicyHolder convertHolder(PosDataEntryTemplate.PolicyHolder policyHolder);

    PosCaseBaseRequest.PolicyInsurant convertInsuredFromHolder(PosDataEntryTemplate.PolicyHolder policyHolder);

    PosCaseBaseRequest.PolicyInsurant convertInsured(PosDataEntryTemplate.PolicyInsured policyInsured);

    List<AttachmentInfo> convertAttachments(List<AttachmentInfo> attachmentInfo);

    @Mapping(target = "super().addresses", expression = "java(convertAddress(source.getAddresses()))")
    PosDataEntryTemplate.PolicyHolder convertPolicyHolder(PolicyCustomerResponse source);

    @Mapping(target = "super().addresses", expression = "java(convertAddress(source.getAddresses()))")
    PosDataEntryTemplate.PolicyInsured convertPolicyInsurant(PolicyCustomerResponse source);

    IndividualCustomerElement.PhoneTemplate customerPHoneToPhoneTemplate(PolicyCustomerResponse.CustomerPhone customerPHone);

    default EmailElement convertEmail(List<CustomerEmail> email) {
        if (CollectionUtils.isEmpty(email)) {
            return null;
        }
        CustomerEmail source = email.stream().sorted(Comparator.comparing(CustomerEmail::getEmail)).findFirst().get();

        EmailElement emailElement = new EmailElement();
        emailElement.setEmail(source.getEmail());
        emailElement.setEmailId(source.getEmailId());
        return emailElement;
    }

    default PosCaseBaseRequest.PosCustomerPhone convertPhone(IndividualCustomerElement.PhoneTemplate phone) {
        if (phone == null) {
            return null;
        }
        PosCaseBaseRequest.PosCustomerPhone posCustomerPhone = new PosCaseBaseRequest.PosCustomerPhone();
        posCustomerPhone.setMobileId(phone.getPhoneId());
        posCustomerPhone.setPhoneNo(phone.getPhoneNo());
        posCustomerPhone.setPhoneType(phone.getPhoneType());
        posCustomerPhone.setCountryCode(phone.getCountryCode());
        return posCustomerPhone;
    }

    default List<IndividualCustomerElement.PhoneTemplate> customerPHoneListToPhoneTemplateList(List<PolicyCustomerResponse.CustomerPhone> source) {
        if (CollectionUtils.isEmpty(source)) {
            return null;
        }
        List<IndividualCustomerElement.PhoneTemplate> target = new ArrayList<IndividualCustomerElement.PhoneTemplate>(source.size());
        for (PolicyCustomerResponse.CustomerPhone customerPHone : source) {
            target.add(customerPHoneToPhoneTemplate(customerPHone));
        }
        if (target.size() > 1) {
            //投被保人是同一个人时候，返回给前端的数据要保持一一对应，不然holder的数据无法convert到insured上面去。
            target.stream().sorted(Comparator.comparing(IndividualCustomerElement.PhoneTemplate::getPhoneNo).thenComparing(IndividualCustomerElement.PhoneTemplate::getCountryCode));
        }
        return target;
    }

    default List<IndividualCustomerElement.AddressTemplate> convertAddress(List<IndividualCustomerElement.AddressTemplate> source) {
        if (CollectionUtils.isEmpty(source)) {
            return null;
        }
        ArrayList<IndividualCustomerElement.AddressTemplate> target = new ArrayList<>(source);
        if (target.size() > 1) {
            //投被保人是同一个人时候，返回给前端的数据要保持一一对应，不然holder的数据无法convert到insured上面去。
            target.stream().sorted(Comparator.comparing(IndividualCustomerElement.AddressTemplate::getAddressType)
                    .thenComparing(address -> StringUtils.join(address.getZipCode(), address.getAddress11(), address.getAddress12(), address.getAddress13(), address.getAddress14(), address.getAddress15(), address.getAddress21(), address.getAddress22(), address.getAddress23(), address.getAddress24(), address.getAddress25())));
        }
        return target;
    }

    @Mapping(target = "phones", expression = "java(convertCenterPhone(customerInfo.getCountryCode(), customerInfo.getPhone()))")
    @Mapping(target = "emails", expression = "java(convertCenterEmail(customerInfo.getEmail()))")
    PosPayerInfo convertCustomer(CustomerCenterIndividualResponse customerInfo);

    default List<PosCustomerPhone> convertCenterPhone(String countryCode, String phone) {
        if (StringUtils.isAnyBlank(countryCode, phone)) {
            return null;
        }
        PosCustomerPhone posCustomerPhone = new PosCustomerPhone();
        posCustomerPhone.setCountryCode(countryCode);
        posCustomerPhone.setPhoneNo(phone);
        return Arrays.asList(posCustomerPhone);
    }

    default List<PosCustomerEmail> convertCenterEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return null;
        }
        PosCustomerEmail posCustomerEmail = new PosCustomerEmail();
        posCustomerEmail.setEmail(email);
        return Arrays.asList(posCustomerEmail);
    }

    PartyCustomerCreateRequest convertCustomerCreate(PosPayerInfo posPayerInfo);
}