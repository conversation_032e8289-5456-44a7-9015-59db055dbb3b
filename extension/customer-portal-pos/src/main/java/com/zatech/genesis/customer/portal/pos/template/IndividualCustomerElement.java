/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.template;

import com.zatech.gaia.resource.components.enums.common.AddressTypeEnum;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.gaia.resource.components.enums.common.MaritalStatusEnum;
import com.zatech.gaia.resource.components.enums.common.PhoneTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.customer.EducationEnum;
import com.zatech.gaia.resource.components.enums.customer.OccupationStatusEnum;
import com.zatech.gaia.resource.components.enums.customer.ResidentialStatusEnum;
import com.zatech.gaia.resource.graphene.common.CountryNationalityEnum;
import com.zatech.gaia.resource.graphene.customer.AccountSubTypeEnum;
import com.zatech.gaia.resource.graphene.customer.AccountTypeEnum;
import com.zatech.gaia.resource.graphene.customer.CustomerTitleEnum;
import com.zatech.gaia.resource.graphene.product.LanguageEnum;
import com.zatech.genesis.customer.portal.pos.builder.IndividualElementSchemaBuilder;
import com.zatech.genesis.customer.portal.biz.common.uimodel.BaseModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.UIModelSystemProperty;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.BuilderParam;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuilder;
import com.zatech.genesis.portal.toolbox.encrypt.annotation.Encrypted;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
public class IndividualCustomerElement extends BaseModel {

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String fullName;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String firstName;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String middleName;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String lastName;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String formattedFullName;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String formattedFirstName;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String formattedMiddleName;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String formattedLastName;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String fullName2;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String firstName2;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String middleName2;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String lastName2;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String formattedFullName2;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String formattedFirstName2;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String formattedMiddleName2;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String formattedLastName2;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String fullName3;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String firstName3;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String middleName3;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private String lastName3;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "yesNo")}
    )
    private YesNoEnum socialSecurity;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "education")}
    )
    private EducationEnum education;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private BigDecimal income;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "yesNo")}
    )
    private YesNoEnum smoke;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private LocalDate smokeGetRidOfDate;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private BigDecimal height;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private BigDecimal weight;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String birthPlace;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private CountryNationalityEnum countryOfBirth;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private LocalDate expiryDateOfCertificate;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private LocalDate issuanceDateOfCertificate;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String issuancePlaceOfCertificate;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String race;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "maritalStatus")}
    )
    private MaritalStatusEnum marriageStatus;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private CustomerTitleEnum title;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    @Encrypted
    private LocalDate birthday;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "gender")}
    )
    private GenderEnum gender;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "country")}
    )
    private CountryNationalityEnum nationality;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "country")}
    )
    private CountryNationalityEnum residenceCountry;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private BigDecimal assets;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "currencys")}
    )
    private CurrencyEnum assetsCurrency;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String loanRefNumber;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String originLoanAmount;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "yesNo")}
    )
    private YesNoEnum isCrossSell;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "language")}
    )
    private LanguageEnum preferredLanguage;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "yesNo")}
    )
    private YesNoEnum followUpLeads;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "yesNo")}
    )
    private YesNoEnum issueWithoutPayment;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "yesNo")}
    )
    private YesNoEnum disabilityOrNot;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "language")}
    )
    private LanguageEnum writtenLanguage;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String organizationName;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String partTimeJob;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String industryCode;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String occupationCode;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "yesNo")}
    )
    private YesNoEnum isPromotional;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "occupationStatus")}
    )
    private OccupationStatusEnum occupationStatus;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "residentialStatus")}
    )
    private ResidentialStatusEnum residentialStatus;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "yesNo")}
    )
    private YesNoEnum deathOrNot;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private LocalDate dateOfDeath;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String customerGrade;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String workingPlace;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String taxNo;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "yesNo")}
    )
    private YesNoEnum ckaIndicator;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private LocalDate ckaEffectiveDate;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String position;

    @UIModelSystemProperty
    @SchemaNodeBuilder(
            value = IndividualElementSchemaBuilder.class,
            params = {@BuilderParam(key = "dictKey", value = "certiType")}
    )
    private CertiTypeEnum certiType;

    @UIModelSystemProperty
    @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
    private String certiNo;

    @UIModelSystemProperty
    private List<AddressTemplate> addresses;

    @UIModelSystemProperty
    private EmailElement email;

    @Getter
    @Setter
    public static class AddressElement {

        @UIModelSystemProperty
        public AddressTemplate address;

        @UIModelSystemProperty
        public AddressTemplate ha;

        @UIModelSystemProperty
        public AddressTemplate ca;

        @UIModelSystemProperty
        public AddressTemplate delivery;

        @UIModelSystemProperty
        public AddressTemplate overseas;

        @UIModelSystemProperty
        public AddressTemplate citizenid;

        @UIModelSystemProperty
        public AddressTemplate contact;

        @UIModelSystemProperty
        public AddressTemplate permanent;

        @UIModelSystemProperty
        public AddressTemplate residential;

        @UIModelSystemProperty
        public AddressTemplate mailing;

        @UIModelSystemProperty
        public AddressTemplate billing;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class AddressTemplate extends BaseModel {

        @UIModelSystemProperty
        private Long addressId;

        @UIModelSystemProperty
        @SchemaNodeBuilder(
                value = IndividualElementSchemaBuilder.class,
                params = {@BuilderParam(key = "dictKey", value = "addressType")}
        )
        private AddressTypeEnum addressType;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String address11;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String address12;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String address13;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String address14;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String address15;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String address21;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String address22;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String address23;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String address24;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String address25;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String zipCode;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String jisCode;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class EmailElement extends BaseModel {

        @UIModelSystemProperty
        private Long emailId;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String email;
    }

    @Getter
    @Setter
    public static class PhoneElement {

        private PhoneTemplate phone;

        private PhoneTemplate landline;

        private PhoneTemplate companyPhone;

        private PhoneTemplate fax;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class PhoneTemplate extends BaseModel {

        @UIModelSystemProperty
        private Long phoneId;

        @UIModelSystemProperty
        @SchemaNodeBuilder(
                value = IndividualElementSchemaBuilder.class,
                params = {@BuilderParam(key = "dictKey", value = "phoneType")}
        )
        private PhoneTypeEnum phoneType;

        @UIModelSystemProperty
        @SchemaNodeBuilder(
                value = IndividualElementSchemaBuilder.class,
                params = {@BuilderParam(key = "dictKey", value = "country")}
        )
        private String countryCode;

        @UIModelSystemProperty
        @SchemaNodeBuilder(IndividualElementSchemaBuilder.class)
        @Encrypted
        private String phoneNo;
    }

    @Getter
    @Setter
    public static class Account extends BaseModel {

        @UIModelSystemProperty
        private Long accountId;

        @UIModelSystemProperty
        private String bankCode;

        @UIModelSystemProperty
        private String bankBranchCode;

        @UIModelSystemProperty
        private String bankBranchName;

        @UIModelSystemProperty
        private AccountTypeEnum accountType;

        @UIModelSystemProperty
        private AccountSubTypeEnum accountSubType;

        @UIModelSystemProperty
        @Encrypted
        private String cardHolderName;

        @UIModelSystemProperty
        @Encrypted
        private String cardNumber;

        @UIModelSystemProperty
        @Encrypted
        private String thirdPartyPayVoucher;

        @UIModelSystemProperty
        private LocalDate expiryDate;

        @UIModelSystemProperty
        @Encrypted
        private String mobileNo;

        @UIModelSystemProperty
        private String safeNo;

        @UIModelSystemProperty
        private String bankName;

        @UIModelSystemProperty
        private String bankCity;

        @UIModelSystemProperty
        private String iban;

        @UIModelSystemProperty
        private String swiftCode;

        @UIModelSystemProperty
        private String bankAddress;

        @UIModelSystemProperty
        private String bankBranchAddress;
    }

    @Getter
    @Setter
    public static class SocialAccount extends BaseModel {

        @UIModelSystemProperty
        private Long socialAccountId;

        @UIModelSystemProperty
        private String socialAccountType;

        @UIModelSystemProperty
        @Encrypted
        private String socialAccountNumber;
    }

}