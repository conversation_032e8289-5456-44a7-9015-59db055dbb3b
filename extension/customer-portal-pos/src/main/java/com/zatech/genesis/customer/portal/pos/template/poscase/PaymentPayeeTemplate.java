package com.zatech.genesis.customer.portal.pos.template.poscase;

import com.zatech.gaia.resource.components.enums.bcp.PayMethodEnum;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.PhoneTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.gaia.resource.graphene.customer.AccountTypeEnum;
import com.zatech.genesis.customer.portal.pos.builder.OnlyDisableFieldSchemaBuilder;
import com.zatech.genesis.customer.portal.pos.builder.PolicyPayeeEnumSchemaBuilder;
import com.zatech.genesis.customer.portal.biz.common.uimodel.BaseModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.Project;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.UIModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.UIModelSystemProperty;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.BuilderParam;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuilder;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;
import com.zatech.genesis.portal.lowcode.framework.common.enums.ModuleTypeEnum;
import com.zatech.genesis.portal.toolbox.encrypt.annotation.Encrypted;

import java.time.LocalDate;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@UIModel(
    name = "Pos payee",
    project = @Project(name = ModuleTypeEnum.customerPortal, productCategories = {FreeMartProductCategoryEnum.ALL}),
    desc = "Ui-model for pos payee.")
public class PaymentPayeeTemplate extends BaseModel implements IUIModel {

    @SchemaNodeBuilder(
        value = PolicyPayeeEnumSchemaBuilder.class,
        params = {@BuilderParam(key = "partyType", value = "partyType")}
    )
    @UIModelSystemProperty
    private PartyTypeEnum partyType;

    @SchemaNodeBuilder(OnlyDisableFieldSchemaBuilder.class)
    @Encrypted
    private String firstName;

    @SchemaNodeBuilder(OnlyDisableFieldSchemaBuilder.class)
    @Encrypted
    private String lastName;

    @SchemaNodeBuilder(OnlyDisableFieldSchemaBuilder.class)
    @Encrypted
    private LocalDate birthday;

    @SchemaNodeBuilder(OnlyDisableFieldSchemaBuilder.class)
    private String countryCode;

    @SchemaNodeBuilder(
        value = PolicyPayeeEnumSchemaBuilder.class,
        params = {@BuilderParam(key = "phoneType", value = "phoneType")}
    )
    @UIModelSystemProperty
    private PhoneTypeEnum phoneType;

    @SchemaNodeBuilder(OnlyDisableFieldSchemaBuilder.class)
    @Encrypted
    private String phoneNo;

    @SchemaNodeBuilder(OnlyDisableFieldSchemaBuilder.class)
    @Encrypted
    private String email;

    @SchemaNodeBuilder(
        value = PolicyPayeeEnumSchemaBuilder.class,
        params = {@BuilderParam(key = "certiType", value = "certiType")}
    )
    @UIModelSystemProperty
    private CertiTypeEnum certiType;

    @SchemaNodeBuilder(OnlyDisableFieldSchemaBuilder.class)
    @Encrypted
    private String certiNo;

    @SchemaNodeBuilder(
        value = PolicyPayeeEnumSchemaBuilder.class,
        params = {@BuilderParam(key = "payMethod", value = "payMethod")}
    )
    @UIModelSystemProperty
    private PayMethodEnum payMethod;

    @SchemaNodeBuilder(
        value = PolicyPayeeEnumSchemaBuilder.class,
        params = {@BuilderParam(key = "accountType", value = "accountType")}
    )
    @UIModelSystemProperty
    private AccountTypeEnum accountType;

    public PartyTypeEnum getPartyType() {
        return partyType;
    }

    public void setPartyType(PartyTypeEnum partyType) {
        this.partyType = partyType;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public LocalDate getBirthday() {
        return birthday;
    }

    public void setBirthday(LocalDate birthday) {
        this.birthday = birthday;
    }

    public PhoneTypeEnum getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(PhoneTypeEnum phoneType) {
        this.phoneType = phoneType;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCertiNo() {
        return certiNo;
    }

    public void setCertiNo(String certiNo) {
        this.certiNo = certiNo;
    }

    public CertiTypeEnum getCertiType() {
        return certiType;
    }

    public void setCertiType(CertiTypeEnum certiType) {
        this.certiType = certiType;
    }

    public PayMethodEnum getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(PayMethodEnum payMethod) {
        this.payMethod = payMethod;
    }

    public AccountTypeEnum getAccountType() {
        return accountType;
    }

    public void setAccountType(AccountTypeEnum accountType) {
        this.accountType = accountType;
    }
}
