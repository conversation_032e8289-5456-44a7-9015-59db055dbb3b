/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.file;

import com.zatech.gaia.resource.file.component.share.enums.file.FileOperateTypeEnum;
import com.zatech.gaia.resource.file.component.share.enums.file.FileSrcTypeEnum;
import com.zatech.genesis.customer.portal.pos.outer.file.request.FileRequest;
import com.zatech.genesis.customer.portal.pos.outer.file.response.FileOperateDefine;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;
import com.zatech.genesis.portal.toolbox.util.ResourceUtil;
import com.zatech.octopus.framework.threadpool.AsyncConfiguration;
import com.zatech.octopus.module.web.dto.ResultBase;

import java.io.InputStream;
import java.util.Map;

import jakarta.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("fm-fileAdapter")
public class FileAdapter {

    private static final String DEFAULT_CONFIG_ID = "default";

    @Resource
    IOuterFileService iOuterFileService;

    @Autowired
    private AsyncConfiguration asyncConfiguration;

    public FileOperateDefine uploadToPrivateBucket(InputStream inputStream, String fileName, String srcId, FileSrcTypeEnum type, Map<String,
        Object> contentMap, String additionalInfo) {

        return uploadFile(inputStream, fileName, srcId, type, DEFAULT_CONFIG_ID, contentMap, additionalInfo);
    }

    private FileOperateDefine uploadFile(InputStream inputStream, String fileName, String srcId, FileSrcTypeEnum type, String configId, Map<String,
        Object> contentMap, String additionalInfo) {
        FileRequest fileRequest = new FileRequest();
        fileRequest.setFileName(fileName);
        fileRequest.setContent(ResourceUtil.readByte(inputStream, true));
        fileRequest.setFileOperateType(FileOperateTypeEnum.UPLOAD_FILE);
        fileRequest.setSrcType(type);
        fileRequest.setSrcObj(srcId);
        fileRequest.setFileType(1);
        fileRequest.setConfigId(configId);
        fileRequest.setContentMap(contentMap);
        fileRequest.setAdditionalInfo(additionalInfo);
        return uploadFile(fileRequest);
    }

    public FileOperateDefine uploadFile(FileRequest fileRequest) {
        log.info("upload file request file name: {},srcType: {}, fileType: {} ", fileRequest.getFileName(), fileRequest.getSrcType(),
            fileRequest.getFileType());
        ResultBase<FileOperateDefine> operateResult = iOuterFileService.operate(fileRequest);
        if (operateResult.getCode() != null) {
            log.warn("upload file failure : {}", JsonParser.toJsonString(operateResult));
        }
        return operateResult.getValue();
    }

    public FileOperateDefine downloadFile(String fileUniqueCode) {
        FileRequest fileRequest = new FileRequest();
        fileRequest.setFileUniqueCode(fileUniqueCode);
        fileRequest.setFileOperateType(FileOperateTypeEnum.DOWNLOAD_FILE);
        log.info("IOuterFileService.operate request: {}", fileRequest);
        ResultBase<FileOperateDefine> operateResult = iOuterFileService.operate(fileRequest);
        return operateResult.getValue();
    }


}
