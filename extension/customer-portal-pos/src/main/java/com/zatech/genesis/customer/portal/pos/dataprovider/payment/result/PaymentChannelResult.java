package com.zatech.genesis.customer.portal.pos.dataprovider.payment.result;

import com.zatech.gaia.resource.components.enums.paymentgateway.PayChannelEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayMethodEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.TradingScenarioEnum;
import com.zatech.genesis.payment.gateway.api.response.PayAccountResp;
import com.zatech.genesis.payment.gateway.enums.ParamKeyEnum;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.FailureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.SuccessResult;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class PaymentChannelResult implements Result {

    private List<PaymentChannel> results;

    @Getter
    @Setter
    @ToString
    @Accessors(chain = true)
    public static class PaymentChannel {

        @Schema(name = "账户code", type = "String")
        private String accountCode;

        @Schema(name = "支付渠道", type = "PayChannelEnum")
        private PayChannelEnum payChannel;

        @Schema(name = "支持的业务场景", type = "List<TradingScenarioEnum>")
        private List<TradingScenarioEnum> tradingScenarioList;

        @Schema(name = "支持的payMethod列表", type = "List<PayMethodEnum>")
        private List<PayMethodEnum> payMethodList;

        @Schema(name = "payMethod级别的扩展配置", type = "Map<PayMethodEnum, Map<ParamKeyEnum, String>>")
        private Map<PayMethodEnum, Map<ParamKeyEnum, String>> payMethodConfig;

        @Schema(name = "payChannel级别的扩展配置", type = "Map<ParamKeyEnum, String>")
        private Map<ParamKeyEnum, String> payChannelConfig;

        public static PaymentChannel from(PayAccountResp entity) {
            return new PaymentChannel()
                .setAccountCode(entity.getAccountCode())
                .setPayChannel(entity.getPayChannel())
                .setPayMethodList(entity.getPayMethodList())
                .setTradingScenarioList(entity.getTradingScenarioList());
        }
    }

    @Override
    public boolean isSuccess() {
        return true;
    }

    @Override
    public SuccessResult succeed() {
        return SuccessResult.from(this);
    }

    @Override
    public FailureResult failed() {
        return null;
    }
}
