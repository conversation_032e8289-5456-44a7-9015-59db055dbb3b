/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.listener.handler;

import com.zatech.gaia.resource.components.enums.common.RelationEnum;
import com.zatech.gaia.resource.components.enums.common.RelationshipWithMainInsuredEnum;
import com.zatech.gaia.resource.customer.scenario.share.dto.base.CustOptionEnum;
import com.zatech.genesis.customer.portal.pos.event.PolicyCustomerLoadEvent;
import com.zatech.genesis.customer.portal.pos.mapper.PosCaseMapper;
import com.zatech.genesis.customer.portal.pos.outer.IOuterCustomerService;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPolicyService;
import com.zatech.genesis.customer.portal.pos.outer.response.PolicyCustomerResponse;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;
import com.zatech.genesis.policy.api.response.PolicyHolderResponse;
import com.zatech.genesis.policy.api.response.PolicyInsurantResponse;
import com.zatech.genesis.policy.api.response.PolicyProductResponse;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.portal.lowcode.framework.api.uimodel.request.UpdateUIModelDataRequest;
import com.zatech.genesis.portal.lowcode.framework.api.uimodel.response.UIModelDataResponse;
import com.zatech.genesis.portal.lowcode.framework.core.service.UIModelService;
import com.zatech.genesis.portal.lowcode.framework.domain.FreeMartOrderUIModelDomain;
import com.zatech.genesis.portal.lowcode.framework.domain.aggregation.FreeMartOrderAggregation;
import com.zatech.genesis.portal.lowcode.framework.domain.aggregation.factory.FreeMartOrderAggregationFactory;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import static com.za.cqrs.util.Functions.doIf;
import static com.zatech.gaia.resource.components.enums.common.TransTypeEnum.POS_HOLDER_INFO_CHANGES;

/**
 * @Author: weizhen.kong
 */
@Slf4j
@Component
@AllArgsConstructor
public class PolicyCustomerLoadHandler extends AbstractListenerHandler<PolicyCustomerLoadEvent> {

    private IOuterPolicyService outerPolicyService;

    private IOuterCustomerService outerCustomerService;

    private final UIModelService uiModelService;

    private final FreeMartOrderAggregationFactory orderAggregationFactory;

    private static final List<CustOptionEnum> OPTIONS = Arrays.asList(CustOptionEnum.BASE, CustOptionEnum.EMAIL, CustOptionEnum.PHONE, CustOptionEnum.ADDRESS);

    @Override
    public void handle(PolicyCustomerLoadEvent event) {
        FreeMartOrderAggregation orderAggregation = orderAggregationFactory.getDomain(event.getOrderNo());
        UIModelDataResponse uiModelDataResponse = orderAggregation.uiModelOpt().map(FreeMartOrderUIModelDomain::toResponse).get();
        JsonMap jsonMap = uiModelDataResponse.getData();
        if (ObjectUtils.anyNotNull(jsonMap.get("policyHolder"), jsonMap.get("policyInsured"))) {
            log.warn("Order uimodel has customer data dont reload policy customer, policyNo: {}", jsonMap.get("policyNo"));
            return;
        }

        PolicyResponse policyResponse = outerPolicyService.queryPolicy(event.getPolicyNO(), null, Boolean.TRUE);

        if (CollectionUtils.isNotEmpty(policyResponse.getPolicyProductList())) {
            Optional<PolicyProductResponse> mainProduct = policyResponse.getPolicyProductList().stream().filter(product -> product.getMainId() == null).findFirst();
            if (mainProduct.isPresent() && CollectionUtils.isNotEmpty(mainProduct.get().getPolicyInsurantList())) {
                PolicyInsurantResponse mainInsurant = mainProduct.get().getPolicyInsurantList().get(0);
                jsonMap.put("policyProductId", mainProduct.get().getPolicyProductId());
                jsonMap.put("policyInsurantId", mainInsurant.getPolicyInsurantId());
                jsonMap.put("insurantCustomerId", mainInsurant.getCustomerId());

                //默认也要把insured的信息查出来，为了让页面勾选了投被保人同步变更的时候，要把holder的信息覆盖insured上面去
                PolicyCustomerResponse policyCustomerResponse = outerCustomerService.queryCustomer(mainInsurant.getCustomerId(), OPTIONS);
                PosDataEntryTemplate.PolicyInsured policyInsurant = PosCaseMapper.MAPPER.convertPolicyInsurant(policyCustomerResponse);
                policyInsurant.setEmail(PosCaseMapper.MAPPER.convertEmail(policyCustomerResponse.getEmails()));
                policyInsurant.setCustomerId(mainInsurant.getCustomerId());
                policyInsurant.setPolicyInsurantId(mainInsurant.getPolicyInsurantId());
                policyInsurant.setRelationshipWithPolicyholder(mainInsurant.getRelationshipWithPolicyholder());
                jsonMap.put("policyInsured", StaticJsonParser.fromObjectToMap(policyInsurant));
                processPolicyHolder(event, jsonMap, policyResponse.getPolicyHolder(), Optional.ofNullable(mainInsurant.getRelationshipWithPolicyholder()).map(relation -> relation == RelationEnum.SELF ? RelationshipWithMainInsuredEnum.SELF : null).orElse(null));
            } else {
                processPolicyHolder(event, jsonMap, policyResponse.getPolicyHolder(), null);
            }
        } else {
            processPolicyHolder(event, jsonMap, policyResponse.getPolicyHolder(), null);
        }

        UpdateUIModelDataRequest modelDataRequest = new UpdateUIModelDataRequest();
        modelDataRequest.setData(jsonMap);
        modelDataRequest.setLockVersion(uiModelDataResponse.getLockVersion());
        modelDataRequest.setExtraInfo(uiModelDataResponse.getExtraInfo());
        uiModelService.updateUIModelData(event.getOrderNo(), modelDataRequest);
    }

    private void processPolicyHolder(PolicyCustomerLoadEvent event, JsonMap jsonMap, PolicyHolderResponse holder, RelationshipWithMainInsuredEnum relationship) {
        doIf(POS_HOLDER_INFO_CHANGES.equals(event.getPosItemType()), () -> {
            PolicyCustomerResponse holderCustomer = outerCustomerService.queryCustomer(holder.getCustomerId(), OPTIONS);
            holderCustomer.setCustomerId(holder.getCustomerId());
            PosDataEntryTemplate.PolicyHolder policyHolder = PosCaseMapper.MAPPER.convertPolicyHolder(holderCustomer);
            policyHolder.setEmail(PosCaseMapper.MAPPER.convertEmail(holderCustomer.getEmails()));
            policyHolder.setRelationshipWithMainInsured(Optional.ofNullable(relationship).orElse(Optional.ofNullable(holder.getRelationshipWithMainInsured()).orElse(RelationshipWithMainInsuredEnum.OTHER)));
            jsonMap.put("policyHolder", StaticJsonParser.fromObjectToMap(policyHolder));
        });
    }

}