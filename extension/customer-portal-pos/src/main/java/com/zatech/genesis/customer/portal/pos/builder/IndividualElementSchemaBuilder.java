/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.builder;

import com.zatech.genesis.customer.portal.pos.builder.param.PosConfigureEnumBuilderParam;
import com.zatech.genesis.customer.portal.pos.outer.IOuterMetadataService;
import com.zatech.genesis.customer.portal.pos.outer.market.response.ApplicationElementsItem;
import com.zatech.genesis.metadata.api.uniquekey.request.SchemaDefUniqueKeyQueryRequest;
import com.zatech.genesis.metadata.api.uniquekey.response.SchemaDefUniqueKeyResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.RichSchema.RichSchema;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.ISchemaNodeBuilder;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildMetaInfo;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.model.EnumItem;

import io.swagger.v3.oas.models.media.Schema;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import static com.za.cqrs.util.Functions.doIf;
import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.APPLICATION_ELEMENT_CONFIG;
import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.COUNTRY_CODE;
import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.EDITABLE_SUFFIX;
import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.SCHEMA_EDITABLE;
import static com.zatech.genesis.customer.portal.pos.common.CommonConstant.SCHEMA_ENUM_NODE;
import static com.zatech.genesis.customer.portal.pos.exception.PosPortalErrorCode.POS_PORTAL_APPLICATION_ELEMENT_CONFIG_EMPTY;

/**
 * @Author: weizhen.kong
 */
@Component
@AllArgsConstructor
public class IndividualElementSchemaBuilder implements ISchemaNodeBuilder<PosConfigureEnumBuilderParam> {

    private IOuterMetadataService outerMetadataService;

    private static final Set<String> CUSTOMER_UNIQUE_KEY = new HashSet<>();

    public synchronized void initUniqueKey() {
        if (CollectionUtils.isNotEmpty(CUSTOMER_UNIQUE_KEY)) {
            return;
        }
        SchemaDefUniqueKeyQueryRequest request = new SchemaDefUniqueKeyQueryRequest();
        request.setSchemaDefType(1);
        List<SchemaDefUniqueKeyResponse> uniqueKeyResponses = outerMetadataService.querySchemaDefUniqueKey(request);
        if (CollectionUtils.isNotEmpty(uniqueKeyResponses)) {
            uniqueKeyResponses.stream().filter(uniqueKeyResponse -> Integer.valueOf(1).equals(uniqueKeyResponse.getCustomerType()))
                    .forEach(uniqueKeyResponse -> CUSTOMER_UNIQUE_KEY.add(uniqueKeyResponse.getFieldCode()));
        }
    }

    @Override
    public PosConfigureEnumBuilderParam buildParam(SchemaNodeBuildContext schemaNodeBuildContext, SchemaNodeBuildMetaInfo schemaNodeBuildMetaInfo) {
        PosConfigureEnumBuilderParam builderParam = new PosConfigureEnumBuilderParam();
        String fieldName = schemaNodeBuildContext.getFieldMeta().getField().getName();
        //具体人的信息相关的枚举使用dictKey标识
        //目前只处理投保人和被保人的application element数据
        List<ApplicationElementsItem> applicationElements = (List<ApplicationElementsItem>) schemaNodeBuildContext.getTemplateParams().get(APPLICATION_ELEMENT_CONFIG);
        CollectionUtils.emptyIfNull(applicationElements).stream().filter(element -> element.getCode().equalsIgnoreCase(fieldName) && CollectionUtils.isNotEmpty(element.getItems())).map(element -> {
            builderParam.setFileCode(element.getCode());
            if (COUNTRY_CODE.equalsIgnoreCase(fieldName)) {
                builderParam.setEnumItems(element.getItems().stream().map(enumItem -> new EnumItem(enumItem.getExtension2(), enumItem.getName())).collect(Collectors.toList()));
            } else {
                builderParam.setEnumItems(element.getItems().stream().map(enumItem -> new EnumItem(enumItem.getEnumItemName(), enumItem.getName())).collect(Collectors.toList()));
            }
            return builderParam;
        }).toList();

        if (CollectionUtils.isEmpty(CUSTOMER_UNIQUE_KEY)) {
            initUniqueKey();
        }

        if (CUSTOMER_UNIQUE_KEY.contains(fieldName)) {
            builderParam.setEditable(false);
        } else {
            builderParam.setEditable(Optional.ofNullable((Boolean) schemaNodeBuildContext.getTemplateParams().get(fieldName + EDITABLE_SUFFIX)).orElse(false));
        }
        return builderParam;
    }

    @Override
    public Schema buildSchema(Schema originalSchema, PosConfigureEnumBuilderParam param, SchemaNodeBuildContext schemaNodeBuildContext, SchemaNodeBuildMetaInfo schemaNodeBuildMetaInfo) {
        originalSchema.addExtension(SCHEMA_ENUM_NODE, param.getEnumItems());
        Optional.ofNullable(param.getEditable()).ifPresent(editable -> originalSchema.addExtension(SCHEMA_EDITABLE, editable));
        RichSchema newSchema = new RichSchema(originalSchema);
        return Optional.ofNullable(param.getEnumItems()).map(item -> newSchema.asEnum()).orElseGet(() -> (Schema) newSchema.asSystem());
    }
}