/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.request;

import com.zatech.gaia.resource.components.enums.common.PhoneTypeEnum;
import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyProductStatusChangeCauseEnum;
import com.zatech.gaia.resource.graphene.posonline.PosStatusStepEnum;
import com.zatech.gaia.resource.graphene.posonline.PosTransEffectiveDateEnum;
import com.zatech.gaia.resource.graphene.product.CancellationTypeEnum;
import com.zatech.genesis.customer.portal.pos.template.AttachmentInfo;
import com.zatech.genesis.customer.portal.pos.template.IndividualCustomerElement;

import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
public class PosCaseBaseRequest {

    private String policyNo;

    private List<PosTransaction> transactionList;

    private List<AttachmentInfo> attachmentInfoList;

    @Getter
    @Setter
    public static class PosTransaction {

        private TransTypeEnum posTransType;

        private PosStatusStepEnum statusStep;

        //pos moduleCode: cancellationReason, transType: POS_CANCELLATION
        private PolicyProductStatusChangeCauseEnum cancellationReason;

        //pos moduleCode: cancellationType, transType: POS_CANCELLATION
        private CancellationTypeEnum cancellationType;

        //pos moduleCode: posEffectiveDateType, transType: FREELOOKSURRENDER,POS_CANCELLATION,POS_HOLDER_INFO_CHANGES
        private PosTransEffectiveDateEnum effectiveDateType;

        //pos moduleCode: posReason 当选择Others的时候，用户输入的信息
        private String reason;

        //pos moduleCode: posReason.propertyCode, transType: FREELOOKSURRENDER,POS_HOLDER_INFO_CHANGES
        private String reasonCode;

        //pos moduleCode: requestDateModule, transType: FREELOOKSURRENDER,POS_CANCELLATION,POS_HOLDER_INFO_CHANGES
        private Date requestDate;

        //pos moduleCode: signDateModule, transType: FREELOOKSURRENDER,POS_CANCELLATION,POS_HOLDER_INFO_CHANGES
        private Date signDate;

        //pos moduleCode: posReason 当选择Life Event的时候，需要输入的数据
        private Date lifeEventDate;

        private PolicyChangeInfoAfter policyChangeInfoAfter;
    }

    @Getter
    @Setter
    public static class PolicyChangeInfoAfter {

        private String posItemCategory;

        private PolicyHolder policyHolder;

        private List<PolicyInsurant> policyInsurantList;
    }

    @Getter
    @Setter
    public static class PolicyHolder extends IndividualCustomerElement {

        private Long customerId;

        private List<PosCustomerPhone> phones;

        private List<EmailElement> emails;

    }

    @Getter
    @Setter
    public static class PolicyProduct {

        private Long policyProductId;

        private List<PolicyInsurant> policyInsurantList;
    }

    @Getter
    @Setter
    public static class PolicyInsurant extends IndividualCustomerElement {

        private Long policyInsurantId;

        private Long customerId;

        private List<PosCustomerPhone> phones;

        private List<EmailElement> emails;
    }

    @Getter
    @Setter
    public static class PosCustomerPhone {

        private Long mobileId;

        private PhoneTypeEnum phoneType;

        private String countryCode;

        private String phoneNo;

        private Map<String, String> extensionMap;
    }

}