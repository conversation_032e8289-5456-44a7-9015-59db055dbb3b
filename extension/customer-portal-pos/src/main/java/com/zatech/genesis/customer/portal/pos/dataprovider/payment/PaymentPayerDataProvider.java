package com.zatech.genesis.customer.portal.pos.dataprovider.payment;

import com.google.common.collect.Lists;
import com.zatech.genesis.customer.portal.pos.dataprovider.payment.request.PaymentConfigRequest;
import com.zatech.genesis.customer.portal.pos.dataprovider.payment.result.PayerResult;
import com.zatech.genesis.customer.portal.pos.exception.PosPortalErrorCode;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPolicyService;
import com.zatech.genesis.customer.portal.pos.template.poscase.PaymentPayeeTemplate;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProviderKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.UIModelDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.IUIModelSchemaBuilder;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;

import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.Resource;

@Slf4j
@UIModelDataProvider(
    name = "payerInfo",
    productCategories = {FreeMartProductCategoryEnum.ALL},
    kind = DataProviderKind.data,
    desc = "根据policyNo获取payer信息")
public class PaymentPayerDataProvider implements IDataProvider<PaymentConfigRequest, PayerResult> {

    @Resource
    private IOuterPolicyService policyService;

    @Resource
    private IUIModelSchemaBuilder uiModelSchemaBuilder;

    @Override
    public PayerResult provide(PaymentConfigRequest paymentRequest, DataProvideContext dataProvideContext) {
        PolicyResponse policyResponse = policyService.queryPolicy(paymentRequest.getPolicyNo(), null, true);
        // todo dont support company
        return Optional.ofNullable(policyResponse.getPolicyPayerList()).orElseGet(Lists::newArrayList).stream()
            .findFirst()
            .map(payer -> {
                PaymentPayeeTemplate template = new PaymentPayeeTemplate();
                template.setPayMethod(payer.getPayMethod());
                Optional.ofNullable(payer.getParty()).ifPresent(party -> {
                    template.setPartyType(party.getPartyType());
                    Optional.ofNullable(party.getPartyCustomer()).ifPresent(customer -> {
                        template.setFirstName(customer.getFirstName());
                        template.setLastName(customer.getLastName());
                        template.setBirthday(customer.getBirthday());
                        Optional.ofNullable(customer.getPhones()).orElseGet(Lists::newArrayList).stream().findFirst().ifPresent(phone -> {
                            template.setPhoneType(phone.getPhoneType());
                            template.setCountryCode(phone.getCountryCode());
                            template.setPhoneNo(phone.getPhoneNo());
                        });
                        Optional.ofNullable(customer.getEmails()).orElseGet(Lists::newArrayList).stream().findFirst().ifPresent(email -> {
                            template.setEmail(email.getEmail());
                        });
                        template.setCertiType(customer.getCertiType());
                        template.setCertiNo(customer.getCertiNo());
                        Optional.ofNullable(customer.getAccounts()).orElseGet(Lists::newArrayList).stream().findFirst().ifPresent(account -> {
                            template.setAccountType(account.getAccountType());
                        });
                    });
                });
                PayerResult result = new PayerResult();
                result.setSchema(uiModelSchemaBuilder.buildSchema(template, new JsonMap()));
                result.setResult(template);
                return result;
            })
            .orElseThrow(() -> CommonException.byError(PosPortalErrorCode.POLICY_NOT_FOUND));
    }
}
