/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.response;

import com.zatech.gaia.resource.components.enums.common.PhoneTypeEnum;
import com.zatech.genesis.customer.portal.pos.template.IndividualCustomerElement;

import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
public class PolicyCustomerResponse extends IndividualCustomerElement {

    private Long customerId;

    private List<CustomerEmail> emails;

    private List<CustomerPhone> phones;

    @Getter
    @Setter
    public static class CustomerEmail {

        private Long emailId;

        private String email;

        private Map<String, String> extensionMap;
    }

    @Getter
    @Setter
    public static class CustomerPhone {

        private Long phoneId;

        private PhoneTypeEnum phoneType;

        private String countryCode;

        private String phoneNo;

        private Map<String, String> extensionMap;
    }

}