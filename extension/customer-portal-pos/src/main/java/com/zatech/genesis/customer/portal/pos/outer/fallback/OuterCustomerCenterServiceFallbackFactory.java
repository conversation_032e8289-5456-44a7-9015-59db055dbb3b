/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.fallback;

import com.zatech.genesis.customer.portal.pos.outer.IOuterCustomerCenterService;
import com.zatech.genesis.customer.portal.pos.outer.response.CustomerCenterIndividualResponse;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;

import org.springframework.stereotype.Component;

/**
 * @Author: weizhen.kong
 */
@Component
public class OuterCustomerCenterServiceFallbackFactory extends AbstractFallbackFactory<IOuterCustomerCenterService> {

    @Override
    public IOuterCustomerCenterService create(Throwable cause) {
        return new IOuterCustomerCenterService() {
            @Override
            public CustomerCenterIndividualResponse getChannelCustomerInfo(Long channelCustomerId) {
                throw outerServiceException(cause);
            }
        };
    }
}