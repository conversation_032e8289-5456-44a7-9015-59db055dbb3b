/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.response;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.schema.CustomerSubTypeEnum;
import com.zatech.gaia.resource.components.enums.schema.CustomerTypeEnum;
import com.zatech.gaia.resource.components.enums.schema.LayoutEnum;
import com.zatech.gaia.resource.components.enums.schema.SchemaDefTypeEnum;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
public class PosItemConfigResponse {

    private String posItemName;

    private String categoryCode;

    private String itemCode;

    private String posConfigStatus;

    private String posIcon;

    private String description;

    private List<PosItemModule> posItemModules;

    @Getter
    @Setter
    public static class PosItemModule {

        private String moduleCode;

        //当前module的数据是否必填
        private YesNoEnum required;

        private CustomerTypeEnum customerType;

        private CustomerSubTypeEnum customerSubType;

        private SchemaDefTypeEnum schemaDefType;

        private List<PosItemModuleProperty> posItemModuleProperties;
    }

    @Getter
    @Setter
    public static class PosItemModuleProperty {

        private String propertyName;

        private String propertyCode;

        private String enumValue;

        private String enumName;

        private Integer orderNo;

        private YesNoEnum selected;

        private YesNoEnum enumFlag;

        private YesNoEnum required;

        private YesNoEnum editable;

        private LayoutEnum dataType;

        private YesNoEnum isExtension;

        private String bizDictKey;

        private String displayName;
    }

}