/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.positems.mock;

import com.zatech.genesis.customer.portal.pos.dataprovider.positems.result.PosItemConfigureResult;

import org.apache.commons.lang3.StringUtils;

/**
 * @Author: weizhen.kong
 */
public class PosItemConfigureMockUtils {

    private PosItemConfigureMockUtils() {
    }

    public static Boolean shouldMock(String policyNo) {
        return StringUtils.isNotEmpty(policyNo) && Boolean.TRUE.equals(policyNo.startsWith("mock"));
    }

    public static PosItemConfigureResult posItemConfigureResult() {
        return new PosDataProviderMockUtil().posItemConfigureResult();
    }

}