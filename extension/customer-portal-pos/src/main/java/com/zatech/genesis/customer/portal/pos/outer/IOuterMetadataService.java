/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer;

import com.zatech.genesis.customer.portal.pos.outer.fallback.MetadataFallbackFactory;
import com.zatech.genesis.customer.portal.pos.outer.request.MetadataDictI18nRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.MetadataDictI18nResponse;
import com.zatech.genesis.metadata.api.bizdict.request.QueryBizDictTenantRequest;
import com.zatech.genesis.metadata.api.bizdict.response.BizDictTenantResponse;
import com.zatech.genesis.metadata.api.uniquekey.request.SchemaDefUniqueKeyQueryRequest;
import com.zatech.genesis.metadata.api.uniquekey.response.SchemaDefUniqueKeyResponse;
import com.zatech.octopus.common.dao.Page;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: weizhen.kong
 */
@FeignClient(value = "zatech-genesis-metadata", url = "${genesis-feign-metadata}", fallbackFactory = MetadataFallbackFactory.class)
public interface IOuterMetadataService {

    @PostMapping(value = "/tenant/dict/bizDict/tenant/page/level")
    Page<MetadataDictI18nResponse> queryTenantBizDictItemsPage(@RequestBody Page<MetadataDictI18nRequest> request);

    @PostMapping(value = "/tenant/dict/queryBizDict")
    List<BizDictTenantResponse> queryBizDictTenant(@RequestBody QueryBizDictTenantRequest request);

    @PostMapping(value = "/query/schema/def/unique-key", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    List<SchemaDefUniqueKeyResponse> querySchemaDefUniqueKey(@RequestBody SchemaDefUniqueKeyQueryRequest schemaDefUniqueKeyQueryRequest);

}