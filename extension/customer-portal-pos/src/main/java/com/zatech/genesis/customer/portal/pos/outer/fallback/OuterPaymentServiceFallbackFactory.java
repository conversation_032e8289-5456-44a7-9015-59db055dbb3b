/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.outer.fallback;

import com.zatech.genesis.customer.portal.pos.dataprovider.payment.request.PaymentRequest;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPaymentService;
import com.zatech.genesis.payment.gateway.api.request.QueryPayOrderReq;
import com.zatech.genesis.payment.gateway.api.response.PayAccountResp;
import com.zatech.genesis.payment.gateway.api.response.PayOrderResp;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;
import com.zatech.octopus.common.dao.Page;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * @Author: weizhen.kong
 */
@Slf4j
@Component
public class OuterPaymentServiceFallbackFactory extends AbstractFallbackFactory<IOuterPaymentService> {

    @Override
    public IOuterPaymentService create(Throwable cause) {
        return new IOuterPaymentService() {

            @Override
            public List<PayAccountResp> getValidPayChannelList(String channelCode) {
                throw outerServiceException(cause, String.format("channelCode: %s", channelCode));
            }

            @Override
            public PayOrderResp pay(PaymentRequest request) {
                throw outerServiceException(cause, String.format("paymentOrder: %s", request.getPayOrderNo()));
            }

            @Override
            public PayOrderResp queryPayOrder(String var1) {
                throw outerServiceException(cause);
            }

            @Override
            public Page<com.zatech.genesis.customer.portal.integration.outer.openapi.payment.response.PayOrderResp> queryPayOrder(QueryPayOrderReq queryPayOrderReq) {
                throw outerServiceException(cause);
            }
        };
    }

    @Override
    public String outerServiceKey() {
        return "payment gateway";
    }
}