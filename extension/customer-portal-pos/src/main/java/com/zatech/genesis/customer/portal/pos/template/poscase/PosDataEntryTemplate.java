/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.template.poscase;

import com.zatech.gaia.resource.components.enums.common.RelationEnum;
import com.zatech.gaia.resource.components.enums.common.RelationshipWithMainInsuredEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyProductStatusChangeCauseEnum;
import com.zatech.gaia.resource.graphene.posonline.PosTransEffectiveDateEnum;
import com.zatech.gaia.resource.graphene.product.CancellationTypeEnum;
import com.zatech.genesis.customer.portal.pos.builder.IndividualElementSchemaBuilder;
import com.zatech.genesis.customer.portal.pos.builder.PosApplicationConfigureEnumSchemaBuilder;
import com.zatech.genesis.customer.portal.pos.builder.PosApplicationConfigureVisibleSchemaBuilder;
import com.zatech.genesis.customer.portal.biz.common.uimodel.BaseModel;
import com.zatech.genesis.customer.portal.pos.template.AttachmentInfo;
import com.zatech.genesis.customer.portal.pos.template.IndividualCustomerElement;
import com.zatech.genesis.customer.portal.pos.template.PayResult;
import com.zatech.genesis.customer.portal.pos.template.PosBaseModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.Project;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.UIModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.UIModelSystemProperty;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.BuilderParam;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuilder;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;
import com.zatech.genesis.portal.lowcode.framework.common.enums.ModuleTypeEnum;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
@UIModel(
        name = "PosDataEntry",
        project = @Project(name = ModuleTypeEnum.customerPortal, productCategories = {FreeMartProductCategoryEnum.ALL}),
        desc = "Ui-model for pos case.")
public class PosDataEntryTemplate extends PosBaseModel {

    private Long policyProductId;

    private Long policyInsurantId;

    private Long insurantCustomerId;

    private PolicyHolder policyHolder;

    private PolicyInsured policyInsured;

    private List<AttachmentInfo> attachmentInfo;

    private PosApplicationInfo posApplicationInfo;

    private PayResult payResult;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class PosApplicationInfo extends BaseModel {

        //pos moduleCode: cancellationReason, transType: POS_CANCELLATION
        @SchemaNodeBuilder(
                value = PosApplicationConfigureEnumSchemaBuilder.class,
                params = {@BuilderParam(key = "cancellationReason", value = "cancellationReason")}
        )
        @UIModelSystemProperty
        private PolicyProductStatusChangeCauseEnum cancellationReason;

        //pos moduleCode: cancellationType, transType: POS_CANCELLATION
        @SchemaNodeBuilder(
                value = PosApplicationConfigureEnumSchemaBuilder.class,
                params = {@BuilderParam(key = "cancellationType", value = "cancellationType")}
        )
        @UIModelSystemProperty
        private CancellationTypeEnum cancellationType;

        //pos moduleCode: posEffectiveDateType, transType: FREELOOKSURRENDER,POS_CANCELLATION,POS_HOLDER_INFO_CHANGES
        @UIModelSystemProperty
        @SchemaNodeBuilder(
                value = PosApplicationConfigureEnumSchemaBuilder.class,
                params = {@BuilderParam(key = "effectiveDateType", value = "posEffectiveDateType")}
        )
        private PosTransEffectiveDateEnum effectiveDateType;

        //pos moduleCode: posReason 当选择Others的时候，用户输入的信息
        @UIModelSystemProperty
        private String reason;

        //pos moduleCode: posReason.propertyCode, transType: FREELOOKSURRENDER,POS_HOLDER_INFO_CHANGES
        @SchemaNodeBuilder(
                value = PosApplicationConfigureEnumSchemaBuilder.class,
                params = {@BuilderParam(key = "reasonCode", value = "posReason")}
        )
        @UIModelSystemProperty
        private String reasonCode;

        //pos moduleCode: requestDateModule, transType: FREELOOKSURRENDER,POS_CANCELLATION,POS_HOLDER_INFO_CHANGES
        @UIModelSystemProperty
        @SchemaNodeBuilder(PosApplicationConfigureVisibleSchemaBuilder.class)
        private Date requestDate;

        //pos moduleCode: signDateModule, transType: FREELOOKSURRENDER,POS_CANCELLATION,POS_HOLDER_INFO_CHANGES
        @UIModelSystemProperty
        @SchemaNodeBuilder(PosApplicationConfigureVisibleSchemaBuilder.class)
        private Date signDate;

        //pos moduleCode: posReason 当选择Life Event的时候，需要输入的数据
        @UIModelSystemProperty
        private Date lifeEventDate;

    }

    @Getter
    @Setter
    public static class PolicyHolder extends IndividualCustomerElement {

        @UIModelSystemProperty
        private Boolean cascadeUpdateInsured;

        @UIModelSystemProperty
        private RelationshipWithMainInsuredEnum relationshipWithMainInsured;

        @UIModelSystemProperty
        private Long customerId;

        @UIModelSystemProperty
        private List<PhoneTemplate> phones;
    }

    @Getter
    @Setter
    public static class PolicyProduct extends BaseModel {

        @UIModelSystemProperty
        private Long policyProductId;

        private List<PolicyInsured> policyInsurantList;
    }

    @Getter
    @Setter
    public static class PolicyInsured extends IndividualCustomerElement {

        @UIModelSystemProperty
        @SchemaNodeBuilder(value = IndividualElementSchemaBuilder.class,
                params = {@BuilderParam(key = "dictKey", value = "relationship")})
        private RelationEnum relationshipWithPolicyholder;

        @UIModelSystemProperty
        private Long customerId;

        @UIModelSystemProperty
        private Long policyInsurantId;

        @UIModelSystemProperty
        private List<PhoneTemplate> phones;
    }

}