/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.model.jsonschema;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.SchemaMetaConstants;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.ISchemaNodeBuilder;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.jsonschema.node.SchemaNodeBuildMetaInfo;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.model.Empty;

import io.swagger.v3.oas.models.media.MapSchema;
import io.swagger.v3.oas.models.media.Schema;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024/1/29 15:06
 * */
@Component
public class ExtensionSchemaBuilder implements ISchemaNodeBuilder<Empty> {

    @Override
    public Empty buildParam(SchemaNodeBuildContext context, SchemaNodeBuildMetaInfo metaInfo) {
        return Empty.Instance;
    }

    @Override
    public Schema buildSchema(Schema originalSchema, Empty param, SchemaNodeBuildContext context, SchemaNodeBuildMetaInfo metaInfo) {
        var schema = new MapSchema();
        schema.addExtension(SchemaMetaConstants.ExtensionKey(), true);
        return schema;
    }
}
