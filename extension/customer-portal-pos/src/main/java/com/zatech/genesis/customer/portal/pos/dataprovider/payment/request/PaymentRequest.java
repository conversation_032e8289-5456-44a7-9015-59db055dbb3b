package com.zatech.genesis.customer.portal.pos.dataprovider.payment.request;

import com.zatech.gaia.resource.components.enums.bcp.ArApEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayChannelEnum;
import com.zatech.genesis.payment.gateway.api.request.PayOrderReq;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModel;
import com.zatech.octopus.component.sleuth.TraceOp;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class PaymentRequest extends PayOrderReq implements IUIModel {

    private String policyNo;

    @Override
    public void setArAp(ArApEnum arAp) {
        super.setArAp(ArApEnum.Ar);
    }

    @Override
    public Map<String, Object> getAttrs() {
        if (this.getPayChannel() == PayChannelEnum.IPAY88) {
            return Collections.emptyMap();
        }
        if (this.getPayChannel() == PayChannelEnum.WSPAY && Objects.nonNull(TraceOp.getLanguage())) {
            Map<String, Object> attrs = super.getAttrs();
            attrs.put("Lang", TraceOp.getLanguage().split("-")[0]);
            return attrs;
        }
        return super.getAttrs();
    }
}