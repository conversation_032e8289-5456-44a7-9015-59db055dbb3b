/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.steps;

import com.zatech.genesis.customer.portal.biz.common.uimodel.step.StepCategoryEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.step.StepFilterFactory;
import com.zatech.genesis.customer.portal.pos.dataprovider.steps.result.PosPageStep;
import com.zatech.genesis.customer.portal.pos.dataprovider.steps.result.PosPageStepsResult;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProviderKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.UIModelDataProvider;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;
import com.zatech.octopus.core.util.JacksonUtil;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: weizhen.kong
 */
@Slf4j
@UIModelDataProvider(
        name = "pageSteps",
        productCategories = {FreeMartProductCategoryEnum.ALL},
        kind = DataProviderKind.data,
        desc = "获取页面的pos流程的steps")
@AllArgsConstructor
public class PosPageStepsDataProvider implements IDataProvider<PosDataEntryTemplate, PosPageStepsResult> {

    private final StepFilterFactory stepFilterFactory;

    @Override
    public PosPageStepsResult provide(PosDataEntryTemplate uiModel, DataProvideContext dataProvideContext) {
        List<PosPageStep> latestSteps = stepFilterFactory.latestSteps(uiModel, StepCategoryEnum.POS);
        log.info("latestSteps:{}", JacksonUtil.toJSONString(latestSteps));
        return new PosPageStepsResult(latestSteps);
    }
}
