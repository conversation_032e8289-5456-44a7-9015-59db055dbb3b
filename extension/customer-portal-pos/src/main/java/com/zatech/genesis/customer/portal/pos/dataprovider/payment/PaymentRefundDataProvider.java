/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.dataprovider.payment;

import com.zatech.genesis.customer.portal.pos.dataprovider.payment.result.PaymentRefundResult;
import com.zatech.genesis.customer.portal.pos.outer.IOuterPosService;
import com.zatech.genesis.customer.portal.pos.outer.request.PosRegisterCaseRequest;
import com.zatech.genesis.customer.portal.pos.outer.response.PosCaseCalculationResponse;
import com.zatech.genesis.customer.portal.pos.template.poscase.PosDataEntryTemplate;
import com.zatech.genesis.customer.portal.pos.util.UiModelReflectUtil;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProviderKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.UIModelDataProvider;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;
import com.zatech.octopus.core.util.JacksonUtil;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: weizhen.kong
 */
@Slf4j
@UIModelDataProvider(
        name = "paymentRefundInfo",
        productCategories = {FreeMartProductCategoryEnum.ALL},
        kind = DataProviderKind.data,
        desc = "是否要收退费以及具体的费用")
@AllArgsConstructor
public class PaymentRefundDataProvider implements IDataProvider<PosDataEntryTemplate, PaymentRefundResult> {

    private IOuterPosService outerPosService;

    @Override
    public PaymentRefundResult provide(PosDataEntryTemplate param, DataProvideContext context) {
        PosRegisterCaseRequest calculateCase = new PosRegisterCaseRequest();
        UiModelReflectUtil.convertUimodel2PosCase(param, calculateCase);
        PosCaseCalculationResponse calculationResponse = outerPosService.calculateCase(calculateCase);
        log.info("Calculate case,order no: {}, response: {}", context.getOrder().getOrderNo(), JacksonUtil.toJSONString(calculationResponse));
        return PaymentRefundResult.from(outerPosService.calculateCase(calculateCase));
    }
}