/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.share;

import java.io.Serializable;

import lombok.Data;

/**
 * Description: new java files header..
 *
 * @version 1.0
 * @date 2025/2/26 14:07
 */
@Data
public class SortField implements Serializable {

    private static final long serialVersionUID = -5552964421218704651L;

    private String fieldName;

    private String sortType;
}
