<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zatech.genesis</groupId>
		<artifactId>customer-portal</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>customer-portal-share</artifactId>
	<name>customer-portal-share</name>
	<description>Customer Portal Share Module</description>
	<packaging>jar</packaging>
	<properties>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.za.cqrs</groupId>
			<artifactId>util</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-openfeign-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zhongan.graphene</groupId>
			<artifactId>zatech-enum-baseline</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jdk8</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis.portal</groupId>
			<artifactId>toolbox-share</artifactId>
			<version>${portal-toolbox.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zhongan.graphene</groupId>
			<artifactId>zatech-enum-product-market</artifactId>
			<version>${revision}</version>
		</dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>zatech-octopus-component-sleuth</artifactId>
        </dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>false</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>flatten</id>
						<phase>process-resources</phase>
						<goals>
							<goal>flatten</goal>
						</goals>
						<configuration>
							<pomElements>
								<distributionManagement>remove</distributionManagement>
								<repositories>remove</repositories>
							</pomElements>
						</configuration>
					</execution>
				</executions>
			</plugin>

		</plugins>
	</build>

</project>
