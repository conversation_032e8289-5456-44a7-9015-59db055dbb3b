package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.permission.enums.common.YesNoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
public class CompanyContactPerson {
    /**
     * 联系人id
     */
    @Schema(title = "Contact person ID", description = "The system-generated unique code used to retrieve the details of the contact person for a customer.")
    private Long contactPersonId;

    /**
     * 姓名
     */
    @Schema(title = "Contact person name", description = "The name of the contact person displayed in the policy or customer details, used by insurance companies to reach the customer if the provided phone number is invalid.")
    private String contactPersonName;

    /**
     * country code
     */
    @Schema(title = "Country code", description = "The code of country, which is a set of alphanumeric characters that uniquely identifies a specific country in various contexts, such as international trade, communication, and finance. Country codes are typically standardized and follow formats like ISO 3166-1 alpha-2 (two-letter codes) or ISO 3166-1 alpha-3 (three-letter codes).")
    private String countryCode;

    /**
     * 电话
     */
    @Schema(title = "Contact person phone number", description = "The specific telephone number associated with an individual who is the contact person of a customer. It typically includes a country code, area code (if applicable), and a unique subscriber number.")
    private String contactPersonPhoneNumber;

    /**
     * 邮箱
     */
    @Schema(title = "Contact person email", description = "The email of the contact person displayed in the policy or customer details, used by insurance companies to reach the customer if the customer's provided contact information is invalid.")
    private String contactPersonEmail;

    /**
     * 职位
     */
    @Schema(title = "Position", description = "The job title or position held by the legal representative within the organization.")
    private String position;

    /**
     * 证件类型
     */
    @Schema(title = "Certi type", description = "The type of certification or document associated with the individual or organization.")
    private CertiTypeEnum certiType;

    /**
     * 证件号码
     */
    @Schema(title = "Certi No.", description = "A unique identifier assigned to a specific certification or document of an individual or organization customer to make it easy to identify and verify. For example, ID number, FIN number, and passport number.")
    private String certiNo;

    /**
     * 是否是primary
     */
    @Schema(title = "Is default", description = "A flag indicating whether the value or setting is the default.")
    private YesNoEnum isDefault;

    /**
     * 是否已删除
     */
    @Schema(title = "Is deleted", description = "Indicates whether the entity or record is deleted.")
    private YesNoEnum isDeleted;

    @Schema(title = "Extension map", description = "A map of extensions associated with the policy.")
    private Map<String, String> extensionMap;

}
