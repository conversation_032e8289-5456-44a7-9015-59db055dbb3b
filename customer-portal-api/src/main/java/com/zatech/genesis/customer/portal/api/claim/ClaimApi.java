/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.claim;

import com.zatech.genesis.customer.portal.api.claim.request.ClaimPreCheckRequest;
import com.zatech.genesis.customer.portal.api.claim.response.ClaimApplicationCaseResponse;
import com.zatech.genesis.customer.portal.api.claim.response.ClaimCaseInformationResponse;
import com.zatech.genesis.customer.portal.api.claim.response.ClaimPreCheckResponse;
import com.zatech.genesis.customer.portal.api.claim.response.Document;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/api/claim")
public interface ClaimApi {

    @PreAuthorize("authenticate() && @checkAuth.checkPolicyNo(#request.policyNo)")
    @PostMapping("/pre-check")
    ClaimPreCheckResponse claimPreCheck(@RequestBody ClaimPreCheckRequest request);

    @PreAuthorize("authenticate() && @checkAuth.checkPolicyNo(#policyNo)")
    @GetMapping("/{policyNo}/list")
    Page<ClaimApplicationCaseResponse> queryClaimApplicationCases(@PathVariable(name = "policyNo") String policyNo,
                                                                  @PageableDefault(sort = "applicationDate", direction = Sort.Direction.DESC) Pageable pageable);

    @PreAuthorize("authenticate() && @checkAuth.checkClaimCaseNo(#policyNo, #caseNo)")
    @GetMapping("/{policyNo}/detail/{caseNo}")
    ClaimCaseInformationResponse queryClaimByCaseNo(@PathVariable(name = "policyNo") String policyNo, @PathVariable(name = "caseNo") String caseNo);

    @PreAuthorize("authenticate() && @checkAuth.checkClaimApplicationNo(#policyNo, #applicationNo)")
    @PutMapping("/{policyNo}/{application-no}/document")
    void updateClaimCaseDocument(@PathVariable(name = "policyNo") String policyNo, @PathVariable(name = "application-no") String applicationNo, @RequestBody List<Document> request);

    @PreAuthorize("authenticate() && @checkAuth.checkClaimApplicationNo(#policyNo, #applicationNo)")
    @GetMapping("/{policyNo}/application-detail/{application-no}")
    ClaimCaseInformationResponse queryClaimByApplicationNo(@PathVariable(name = "policyNo") String policyNo, @PathVariable(name = "application-no") String applicationNo);


}