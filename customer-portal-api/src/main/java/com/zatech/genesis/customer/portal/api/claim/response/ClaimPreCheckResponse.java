/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.claim.response;

import com.zatech.gaia.resource.components.enums.claim.ClaimTypeEnum;
import com.zatech.genesis.portal.toolbox.share.model.EnumItem;

import java.util.List;
import java.util.Set;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ClaimPreCheckResponse {

    private Boolean pass;

    private String goodsCode;

    private String message;

    private Set<EnumItem> claimTypeList;

    private Set<EnumItem> claimIncidentReasonList;

    private List<ClaimTypeToReasonMap> claimTypeToReasonMap;

    @Getter
    @Setter
    public static class ClaimTypeToReasonMap {

        private ClaimTypeEnum claimType;

        private Set<String> claimIncidentReasons;
    }

}