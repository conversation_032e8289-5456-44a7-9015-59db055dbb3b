package com.zatech.genesis.customer.portal.api.user;

import com.zatech.genesis.customer.portal.api.user.request.QueryUserIdentifyRequest;
import com.zatech.genesis.customer.portal.api.user.response.QueryUserIdentifyResponse;

import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/11/10
 */
@Tag(name = "User Resource API V2")
@RequestMapping(path = "/api/users")
public interface UserApiResourceV2 {

    @PostMapping(value = "/check-identify")
    @PreAuthorize("authenticate()")
    List<QueryUserIdentifyResponse> queryIdentifyUser(@RequestBody QueryUserIdentifyRequest request);

    @GetMapping(value = "/related-account")
    @PreAuthorize("authenticate()")
    List<QueryUserIdentifyResponse> queryAnotherUserAccount();

}
