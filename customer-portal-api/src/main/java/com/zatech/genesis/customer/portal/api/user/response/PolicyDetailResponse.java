package com.zatech.genesis.customer.portal.api.user.response;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyStatusEnum;
import com.zatech.gaia.resource.components.enums.product.CoveragePeriodTypeEnum;
import com.zatech.genesis.customer.portal.api.user.base.Beneficiary;
import com.zatech.genesis.customer.portal.api.user.base.Channel;
import com.zatech.genesis.customer.portal.api.user.base.File;
import com.zatech.genesis.customer.portal.api.user.base.Insured;
import com.zatech.genesis.customer.portal.api.user.base.InsuredObjectResponse;
import com.zatech.genesis.customer.portal.api.user.base.PayerInfo;
import com.zatech.genesis.customer.portal.api.user.base.PolicyHolder;
import com.zatech.genesis.customer.portal.api.user.base.ProductSummary;
import com.zatech.genesis.customer.portal.api.user.base.PurchaseSummary;

import com.zatech.genesis.customer.portal.api.user.base.SpecialAgreement;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
@Schema(name = "policy detail", description = "policy detail")
public class PolicyDetailResponse extends PolicyBaseResponse {

    private Long goodsId;

    private Long goodsPlanId;

    @Schema(title = "goodsCode")
    private String goodsCode;

    @Schema(title = "goodsName")
    private String goodsName;

    @Schema(title = "policyNo")
    private String policyNo;

    @Schema(title = "status")
    private PolicyStatusEnum status;

    @Schema(title = "premium")
    private String premium;

    @Schema(title = "commission")
    private String commission;

    @Schema(title = "commissionCurrency")
    private CurrencyEnum commissionCurrency;

    @Schema(title = "paymentStatus")
    private String paymentStatus;

    @Schema(title = "policyHolder")
    private PolicyHolder policyHolder;

    @Schema(title = "hasAttachment")
    private YesNoEnum hasAttachment;

    @Schema(title = "insureds")
    private List<Insured> insureds;

    @Schema(title = "beneficiaries")
    private List<Beneficiary> beneficiaries;

    @Schema(title = "insuredObjectList")
    private List<InsuredObjectResponse> insuredObjectList;

    @Schema(title = "proposalInsuredObjectResponse")
    private List<File> attachmentList;

    @Schema(title = "purchaseSummary")
    private PurchaseSummary purchaseSummary;

    @Schema(title = "productSummary")
    private ProductSummary productSummary;

    @Schema(title = "支付人信息")
    private List<PayerInfo> policyPayerList;

    @Schema(title = "Relation NO.")
    private List<String> relationNos;

    @Schema(title = "Schema")
    private String schema;

    @Schema(title = "channel")
    private Channel channel;

    private Map<String, String> extensions;

    @Schema(title = "effectiveDate")
    private Date effectiveDate;

    @Schema(title = "expireDate")
    private Date expireDate;

    @Schema(title = "保障期间类型:1：天，2：周，3：月，4：年，5：年满型，6：岁满型，7：终身型")
    private CoveragePeriodTypeEnum coveragePeriodType;

    @Schema(title = "保障期间")
    private String coveragePeriod;

    @Schema(title = "Special agreement")
    private List<SpecialAgreement> specialAgreements;

    @Schema(title = "返回的约信息")
    private List<PolicyClauseFileResponse> policyClauseFileResponses;

    @Schema(title = "Zone ID")
    private String zoneId;

    @Schema(title = "Status change reason")
    private String statusChangeReason;

    @Schema(title = "Status change date")
    private Date statusChangeDate;
}
