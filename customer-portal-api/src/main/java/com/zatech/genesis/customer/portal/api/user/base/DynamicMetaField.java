/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-03-13 9:39
 */
@Schema(title = "动态元数据字段")
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class DynamicMetaField {

    @Schema(title = "扩展字段集合", description = "动态添加扩展字段,Map.Entry.Key与Map.Entry.Value目前只支持String")
    Map<String, String> extensions;
}
