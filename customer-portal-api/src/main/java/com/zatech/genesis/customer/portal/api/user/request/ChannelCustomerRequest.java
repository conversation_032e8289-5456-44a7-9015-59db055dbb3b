package com.zatech.genesis.customer.portal.api.user.request;

import com.zatech.genesis.customer.portal.api.user.base.BaseCustomerComponentDTO;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/11/1
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
public class ChannelCustomerRequest extends BaseCustomerComponentDTO {

    @Schema(title = "channelCode")
    private String channelCode;

    private UpdateIndividualMergeRequest merge;

    private ValidateVerificationCodeRequest validateVerification;

}
