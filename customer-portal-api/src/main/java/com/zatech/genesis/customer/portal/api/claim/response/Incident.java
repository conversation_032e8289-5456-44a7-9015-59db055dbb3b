package com.zatech.genesis.customer.portal.api.claim.response;

import com.zatech.gaia.resource.components.enums.claim.ClaimTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoUnknownEnum;
import com.zatech.gaia.resource.graphene.common.CountryNationalityEnum;
import com.zatech.genesis.customer.portal.api.customer.Address;
import com.zatech.genesis.customer.portal.api.customer.DynamicMetaField;
import com.zatech.genesis.customer.portal.api.i18n.I18n;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class Incident extends DynamicMetaField {

    private List<String> liabilityReason;

    @I18n(key = "claimsAccidentReason")
    private List<ClaimTypeEnum> claimTypes;

    /**
     * matedata 租户层级 可配置, 单次只能选一个
     * Incident Reason: Delay ,Cancellation, Accident
     */
    @I18n(key = "claimIncidentType")
    private String incidentReason;

    private String description;

    private String zoneId;

    /**
     * 使用address
     */
    private String incidentPlace;

    /**
     * 通常travel 需要填 涉及跨国
     */
    private CountryNationalityEnum incidentCountry;

    private Date incidentDate;

    /**
     * 车险事故场景
     */
    private String policeStation;

    /**
     * 车险事故场景
     * 前端如果policeStation是Other，需要手动输入一个text。这个时候会传到这个字段上
     */
    private String policeStationManualText;

    /**
     * 车险事故场景, 是否报警
     */
    private YesNoUnknownEnum notifyPolice;

    private Address address;

}