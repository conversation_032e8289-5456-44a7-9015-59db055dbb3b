package com.zatech.genesis.customer.portal.api.user.response;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.product.CoveragePeriodTypeEnum;
import com.zatech.gaia.resource.components.enums.product.PayFrequencyTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
@Schema(name = "policy list detail", description = "policy list detail")
public class PolicyListResponse {

    @Schema(title = "productName")
    private String goodsName;

    @Schema(title = "policyNo")
    private String policyNo;

    @Schema(title = "status")
    private String status;

    @Schema(title = "paymentStatus")
    private String paymentStatus;

    @Schema(title = "policyholderName")
    private String policyholderName;

    @Schema(title = "policyholderType")
    private String policyholderType;

    @Schema(title = "hasAttachment")
    private YesNoEnum hasAttachment;

    @Schema(title = "bizApplyNo")
    private String bizApplyNo;

    @Schema(title = "transactionNo")
    private String transactionNo;

    @Schema(title = "open policy")
    private YesNoEnum openPolicy;

    @Schema(title = "relationNo")
    private String relationNo;

    @Schema(title = "premium")
    private String premium;

    @Schema(title = "currency")
    private CurrencyEnum currency;

    @Schema(title = "commission")
    private String commission;

    @Schema(title = "commissionCurrency")
    private CurrencyEnum commissionCurrency;

    @Schema(title = "isRenewalPolicy")
    private YesNoEnum isRenewalPolicy;

    @Schema(title = "agentCode")
    private String agentCode;

    @Schema(title = "insuredName")
    private String insuredName;

    @Schema(title = "effectiveDate")
    private Date effectiveDate;

    @Schema(title = "expireDate")
    private Date expireDate;

    @Schema(title = "保障期间类型:1：天，2：周，3：月，4：年，5：年满型，6：岁满型，7：终身型")
    private CoveragePeriodTypeEnum coveragePeriodType;

    @Schema(title = "保障期间")
    private String coveragePeriod;

    @Schema(title = "Payment frequency")
    private PayFrequencyTypeEnum paymentFrequency;

    @Schema(title = "Has overdue premium")
    private YesNoEnum hasOverduePremium;

    @Schema(title = "Zone ID")
    private String zoneId;

    @Schema(title = "Goods Id")
    private Long goodsId;

    @Schema(title = "Status change cause")
    private String statusChangeCauseCode;

    @Schema(title = "Status change reason")
    private String statusChangeReason;

    @Schema(title = "Status change date")
    private Date statusChangeDate;

}
