/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.payment.response;

import com.zatech.genesis.customer.portal.api.payment.enums.PayStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class QueryPaymentInfoResponse {

    @Schema(name = "pay status")
    private PayStatusEnum status;

    @Schema(name = "third order no")
    private String thirdPartyOrderNo;

    @Schema(name = "redirect url")
    private String redirectUrl;

    @Schema(name = "amount")
    private String amount;

    @Schema(name = "currency")
    private String currency;

    @Schema(name = "pay date")
    private Date payDate;

    @Schema(name = "form param, use to redirectUrl")
    private Map<String, Object> formParams = new HashMap<>();

}
