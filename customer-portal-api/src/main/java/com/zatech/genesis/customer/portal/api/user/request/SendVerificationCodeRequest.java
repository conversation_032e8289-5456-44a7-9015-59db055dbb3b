package com.zatech.genesis.customer.portal.api.user.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString

public class SendVerificationCodeRequest {

    private String nonce;

    private String scenarioType;

    private String sendType;

    private String destination;

    private String captchaCode;

}
