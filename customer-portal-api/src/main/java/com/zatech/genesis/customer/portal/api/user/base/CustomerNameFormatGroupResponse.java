package com.zatech.genesis.customer.portal.api.user.base;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/10
 */
@Getter
@Setter
@ToString(callSuper = true)
public class CustomerNameFormatGroupResponse {
    /**
     * {@link com.zatech.gaia.resource.components.enums.metadata.CustomerNameSeparatorEnum}
     */
    private Integer nameConnectionSeparator;

    private List<CustomerNameSchemaFieldResponse> fields;
}
