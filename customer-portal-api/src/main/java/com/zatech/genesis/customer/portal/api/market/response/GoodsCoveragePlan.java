/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.market.response;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025/3/14 10:25
 */
@Data
public class GoodsCoveragePlan {

    private Long planId;

    private String planCode;

    private String goodsPlanName;

    private YesNoEnum isForShow;

    private Date showStartTime;

    private Date showEndTime;

    private Integer maxSalesCount;

    private BigDecimal minPrice;

    private Long packageId;

    private Long techProductId;

    private List<I18nMessage> i18nPlanName;

}
