package com.zatech.genesis.customer.portal.api.user.base;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/11/13
 */
public enum PortalPolicyListEnum {
    EFFECTIVE("effective", "10", "policy effect"),
    TERMINATED("terminated", "11", "termination"),
    LAPSED("lapsed", "12,14", "expiry,lapsed");

    private String code;

    private String statusMapping;

    private String desc;

    PortalPolicyListEnum(String code, String statusMapping, String desc) {
        this.code = code;
        this.statusMapping = statusMapping;
        this.desc = desc;
    }

    public static Set<String> getStatusMapping(PortalPolicyListEnum status) {
        if (status == null) {
            return new HashSet<>();
        }
        return new HashSet<>(Arrays.asList(status.statusMapping.split(",")));
    }
}
