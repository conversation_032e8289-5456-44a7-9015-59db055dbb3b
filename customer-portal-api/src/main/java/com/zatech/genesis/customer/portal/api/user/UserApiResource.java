package com.zatech.genesis.customer.portal.api.user;

import com.zatech.genesis.customer.portal.api.user.request.ChangeEmailRequest;
import com.zatech.genesis.customer.portal.api.user.request.ChangeMobileRequest;
import com.zatech.genesis.customer.portal.api.user.request.ChangePasswordRequest;
import com.zatech.genesis.customer.portal.api.user.request.ChannelCustomerRequest;
import com.zatech.genesis.customer.portal.api.user.request.QueryUserIdentifyRequest;
import com.zatech.genesis.customer.portal.api.user.request.ResetPasswordRequest;
import com.zatech.genesis.customer.portal.api.user.request.SchemaDefUniqueKeyRequest;
import com.zatech.genesis.customer.portal.api.user.response.ChannelCustomerResponse;
import com.zatech.genesis.customer.portal.api.user.response.CustomerNameFormatResponse;
import com.zatech.genesis.customer.portal.api.user.response.QueryUserIdentifyResponse;
import com.zatech.genesis.customer.portal.api.user.response.SchemaDefUniqueKeyResponse;
import com.zatech.genesis.customer.portal.api.user.response.UserSummaryInfoResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import scala.Unit;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * <AUTHOR>
 * @date 2023/11/10
 */
@Tag(name = "User Resource API")
@RequestMapping(path = "/api/users")
public interface UserApiResource {

    @Operation(summary = "get channel customer info")
    @GetMapping("/info")
    @PreAuthorize("authenticate()")
    ChannelCustomerResponse getChannelCustomerInfo();

    @Operation(summary = "get my page summary")
    @GetMapping("/summary")
    @PreAuthorize("authenticate()")
    UserSummaryInfoResponse getUserSummaryInfo();

    @Operation(summary = "update channel customer info")
    @PutMapping("/info")
    @PreAuthorize("authenticate()")
    void updateChannelCustomerInfo(@RequestBody ChannelCustomerRequest request);

    @Operation(summary = "query tenant customer unique key define")
    @PostMapping("/schema/def/unique-key")
    @PreAuthorize("authenticate()")
    List<SchemaDefUniqueKeyResponse> getSchemaDefUniqueKey(@RequestBody SchemaDefUniqueKeyRequest request);

    @GetMapping(value = "/name-format")
    @PreAuthorize("authenticate()")
    @Operation(summary = "query customer name group and name format.")
    CustomerNameFormatResponse getCustomerNameFormat();

    @PostMapping(value = "/channel-customers/check-identify")
    @PreAuthorize("authenticate()")
    List<QueryUserIdentifyResponse> queryIdentifyUser(@RequestBody QueryUserIdentifyRequest request);

    @GetMapping(value = "/channel-customers/related-account")
    @PreAuthorize("authenticate()")
    List<QueryUserIdentifyResponse> queryAnotherUserAccount();

    @Operation(summary = "update channel customer info")
    @PutMapping("/info/cascade-account")
    @PreAuthorize("authenticate()")
    void updateChannelCustomerInfoCascade(@RequestBody ChannelCustomerRequest request);

    @Operation(summary = "Change channel customer password")
    @PreAuthorize("authenticate()")
    @PostMapping(value = "/channel-customers/change-password")
    void changePassword(@RequestBody ChangePasswordRequest request);

    @Operation(summary = "Logout")
    @PreAuthorize("authenticate()")
    @PostMapping(params = "logout", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    Unit logout();

    @Operation(summary = "Reset Password")
    @PostMapping(params = "reset-password", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    @PreAuthorize("authenticate()")
    void resetPassword(@RequestBody ResetPasswordRequest request);

    @Operation(summary = "Change Email")
    @PostMapping(params = "change-email", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    @PreAuthorize("authenticate()")
    void changeChannelCustomerEmail(@RequestBody ChangeEmailRequest request);

    @Operation(summary = "Change mobile")
    @PostMapping(params = "change-mobile", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    @PreAuthorize("authenticate()")
    void changeChannelCustomerMobile(@RequestBody ChangeMobileRequest request);

}
