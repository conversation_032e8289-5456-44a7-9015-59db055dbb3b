/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Schema(name = "Channel", description = "Channel")
@Accessors(chain = true)
public class Channel {
    @Schema(title = "channelCode")
    private String channelCode;

    @Schema(title = "channelName")
    private String channelName;

    @Schema(title = "branchCode")
    private String branchCode;

    @Schema(title = "branchName")
    private String branchName;

    @Schema(title = "agreementCode")
    private String agreementCode;

    @Schema(title = "agreementName")
    private String agreementName;

    @Schema(title = "agentCode")
    private String agentCode;

    @Schema(title = "agentName")
    private String agentName;
}
