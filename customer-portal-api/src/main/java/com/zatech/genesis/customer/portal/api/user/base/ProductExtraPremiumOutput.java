package com.zatech.genesis.customer.portal.api.user.base;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zatech.gaia.resource.components.enums.product.AdditionalTypeEnum;
import com.zatech.gaia.resource.components.enums.product.LoadingMethodEnum;
import com.zatech.genesis.customer.portal.api.user.util.DecimalSerializer;

import java.math.BigDecimal;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class ProductExtraPremiumOutput {

    /**
     * 加费类型 {@link AdditionalTypeEnum}
     */
    private AdditionalTypeEnum loadingType;

    /**
     * 加费方式  {@link LoadingMethodEnum}
     */
    private LoadingMethodEnum loadingMethod;

    /**
     * 顺序
     */
    private Integer orderNo;

    /**
     * 折扣比例
     */
    private String rate;

    /**
     * 加费(是否含税取决于产品上的含税配置)
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodExtraPremium;

    /**
     * periodExtraPremiumBeforeCap
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodExtraPremiumBeforeCap;

    /**
     * periodExtraPremiumCapDelta
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodExtraPremiumCapDelta;

    /**
     * 年化加费(是否含税取决于产品上的含税配置)
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal annualExtraPremium;

    /**
     * 加费后的金额
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodPremiumAfterCurrentCalculation;

    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalExtraPremium;

    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalPremiumAfterCurrentCalculation;
}
