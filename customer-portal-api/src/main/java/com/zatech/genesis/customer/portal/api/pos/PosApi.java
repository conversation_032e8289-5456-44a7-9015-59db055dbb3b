/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.pos;

import com.zatech.genesis.customer.portal.api.customer.Document;
import com.zatech.genesis.customer.portal.api.pos.response.PosDetailResponse;
import com.zatech.genesis.customer.portal.api.pos.response.PosPreCheckResponse;
import com.zatech.genesis.customer.portal.api.pos.response.PosQueryListResultResponse;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/api/pos")
public interface PosApi {

    @PreAuthorize("authenticate() && @checkAuth.checkPolicyNo(#policyNo)")
    @PostMapping("/{policy-no}/pre-check")
    PosPreCheckResponse preCheck(@PathVariable("policy-no") String policyNo);

    @PreAuthorize("authenticate() && @checkAuth.checkPolicyNo(#policyNo)")
    @GetMapping("/{policy-no}/list")
    Page<PosQueryListResultResponse> queryPosList(@PathVariable(name = "policy-no") String policyNo,
                                                  @PageableDefault(sort = "applicationDate", direction = Sort.Direction.DESC) Pageable pageable);

    @PreAuthorize("authenticate() && @checkAuth.checkPosCaseNo(#policyNo, #caseNo)")
    @GetMapping("/{policy-no}/detail/{case-no}")
    PosDetailResponse queryPosDetail(@PathVariable(name = "policy-no") String policyNo, @PathVariable(name = "case-no") String caseNo);

    @PreAuthorize("authenticate() && @checkAuth.checkPosCaseNo(#policyNo, #caseNo)")
    @PostMapping("/{policy-no}/case/{case-no}/attachments")
    void saveAttachments(@PathVariable(name = "policy-no") String policyNo,
                         @PathVariable("case-no") String caseNo,
                         @RequestBody List<Document> attachmentRequests);

}