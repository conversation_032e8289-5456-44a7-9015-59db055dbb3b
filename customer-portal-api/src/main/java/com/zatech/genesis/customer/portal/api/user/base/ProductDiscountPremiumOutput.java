/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zatech.gaia.resource.components.enums.product.PremiumDiscountTypeEnum;
import com.zatech.genesis.customer.portal.api.user.util.DecimalSerializer;

import java.math.BigDecimal;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class ProductDiscountPremiumOutput {
    /**
     * 折扣类型
     */
    private PremiumDiscountTypeEnum premiumDiscountType;

    /**
     * 折扣保费(是否含税取决于产品上的含税配置)
     * 折扣金额
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodDiscountPremium;

    /**
     * periodDiscountPremiumBeforeCap
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodDiscountPremiumBeforeCap;

    /**
     * periodDiscountPremiumCapDelta
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodDiscountPremiumCapDelta;

    /**
     * 年化折扣保费(是否含税取决于产品上的含税配置)
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal annualDiscountPremium;

    /**
     * 折扣顺序(用于展示折扣明显,回账)
     */
    private Integer orderNo;

    /**
     * 折扣比例
     */
    private String rate;

    /**
     * 折扣后金额
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodPremiumAfterCurrentCalculation;

    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalPremiumDiscount;

    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalPremiumAfterCurrentCalculation;
}
