/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.related;

import com.zatech.genesis.customer.portal.api.user.request.QueryDictRequest;
import com.zatech.genesis.customer.portal.api.user.response.BizDictTenantResponse;
import com.zatech.genesis.customer.portal.api.user.response.QueryDictResponse;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: weizhen.kong
 */
@RequestMapping(path = "/api/users")
public interface RelatedApiResource {

    @PostMapping(value = "/related-dict")
    @PreAuthorize("authenticate()")
    List<QueryDictResponse> queryDict(@RequestBody QueryDictRequest request);

    @PostMapping(value = "/query/biz-dict")
    @PreAuthorize("authenticate()")
    List<BizDictTenantResponse> queryBizDict(@RequestBody QueryDictRequest request);

}