/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.genesis.customer.portal.api.user.util.DecimalSerializer;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/11/5
 */
@Setter
@Getter
@Schema(name = "PurchaseSummary", description = "PurchaseSummary")
public class PurchaseSummary {

    /**
     * 基准保额
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal sumInsure;

    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal basePrice;

    /**
     * periodNoClaimDiscount
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodNoClaimDiscount;

    /**
     * coverageNoClaimDiscount
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageNoClaimDiscount;

    /**
     * 期缴最终总费用
     * periodFinalPremium = periodStandardPremium
     * + periodExtraPremium
     * - periodDiscountPremium
     * + periodTotalTax
     * + stampDuty
     * + periodLevy
     * + totalServiceFee
     * + totalCommissionFee
     * - periodCampaignDiscount
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodFinalPremium;

    /**
     * Insurance Tax
     * the total tax of coverage total
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalTax;

    /**
     * Total Premium Before Tax
     * the net premium of coverage total
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalNetPremium;

    /**
     * 险种费用明细
     */
    private List<ProductSaPremium> products;

    /**
     * 除了主险以外,所有附加险的discount总和
     */
    private BigDecimal allRiderCoverageTotalPremiumDiscount;

    /**
     * 总-每期明细
     */
    private List<PurchaseSummary> totalPeriodDetails;

    /**
     * 分期 - 总保费
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalFinalPremium;

    /**
     * 期缴详情
     */
    private List<PeriodFinalPremiumSummary> periodFinalPremiumInfos;

    private List<ProductPremium> netPremiums;

    @Schema(title = "netPremiumSum")
    private String netPremiumSum;

    @Schema(title = "installmentPremiumBeforeTaxAndServiceFee")
    private String installmentPremiumBeforeTaxAndServiceFee;

    @Schema(title = "tax")
    private String tax;

    @Schema(title = "levy")
    private String levy;

    @Schema(title = "stampDuty")
    private String stampDuty;

    @Schema(title = "discount")
    private String discount;

    @Schema(title = "sum")
    private String sum;

    private CurrencyEnum currency;

    @Getter
    public static class PeriodFinalPremiumSummary {

        @JsonSerialize(using = DecimalSerializer.class)
        private BigDecimal premium;

        /**
         * 第几期
         */
        private PeriodTimeType whichTime;

        private PeriodFinalPremiumSummary(BigDecimal premium, PeriodTimeType type) {
            this.premium = premium;
            this.whichTime = type;
        }

        public static PeriodFinalPremiumSummary first(BigDecimal premium) {
            return new PeriodFinalPremiumSummary(premium, PeriodTimeType.FIRST);
        }

        public static PeriodFinalPremiumSummary other(BigDecimal premium) {
            return new PeriodFinalPremiumSummary(premium, PeriodTimeType.OTHER);
        }

    }

    public enum PeriodTimeType {
        FIRST, OTHER
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class ProductPremium {
        @Schema(title = "netPremium")
        private String netPremium;

        @Schema(title = "productName")
        private String productName;

        private CurrencyEnum currency;

    }

}
