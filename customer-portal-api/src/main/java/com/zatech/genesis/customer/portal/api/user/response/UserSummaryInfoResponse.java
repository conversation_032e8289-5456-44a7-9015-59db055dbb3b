package com.zatech.genesis.customer.portal.api.user.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
@ToString
public class UserSummaryInfoResponse {

    private Long personId;

    private Long channelCustomerId;

    private ChannelCustomerResponse channelCustomerInfo;

    private Integer policyNumber;

    private Integer orderNumber;

}
