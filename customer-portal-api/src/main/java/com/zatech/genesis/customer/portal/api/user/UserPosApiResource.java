/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user;

import com.zatech.genesis.customer.portal.api.user.response.OrderClaimResponse;
import com.zatech.genesis.customer.portal.api.user.response.OrderPosResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @Author: weizhen.kong
 */
@Tag(name = "User's Pos Domain Resource API")
@RequestMapping(path = "/api/users")
public interface UserPosApiResource {

    @Operation(summary = "Get Login User's pos case")
    @PreAuthorize("authenticate() && @orderCheck.authorize(#orderNo)")
    @GetMapping("/{order-no}/pos")
    @ResponseBody
    OrderPosResponse queryPosCase(@PathVariable("order-no") String orderNo);

    @Operation(summary = "Get Login User's claim case")
    @PreAuthorize("authenticate() && @orderCheck.authorize(#orderNo)")
    @GetMapping("/{order-no}/claim")
    @ResponseBody
    OrderClaimResponse queryClaimCase(@PathVariable("order-no") String orderNo);

}