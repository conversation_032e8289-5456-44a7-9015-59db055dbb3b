package com.zatech.genesis.customer.portal.api.user.request;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@Accessors(chain = true)
@ToString
@RequiredArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", include = JsonTypeInfo.As.EXISTING_PROPERTY, visible = true)
@JsonSubTypes(value = {
    @JsonSubTypes.Type(value = EmailLoginRequest.class, name = EmailLoginRequest.TYPE),
    @JsonSubTypes.Type(value = PhoneLoginRequest.class, name = PhoneLoginRequest.TYPE),
})
public abstract class LoginRequest {

    private final String type;

    private String channelCode;

    @NotBlank(message = "{verification.code.NotBlank.message}")
    private String verificationCode;

    private String nonce;

    private boolean defaultRegister;

}
