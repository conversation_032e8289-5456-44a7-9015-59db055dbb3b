/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.response;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.graphene.posonline.PosItemCategoryEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
public class PolicyPosItemsResponse {

    @Schema(title = "The category of POS Item")
    private PosItemCategoryEnum posItemCategory;

    /**
     * 保全项CODE
     */
    @Schema(title = "The item of POS")
    private TransTypeEnum posItemType;

    /**
     * 保全项自定义显示名
     */
    @Schema(title = "The name of customization")
    private String posItemName;

}