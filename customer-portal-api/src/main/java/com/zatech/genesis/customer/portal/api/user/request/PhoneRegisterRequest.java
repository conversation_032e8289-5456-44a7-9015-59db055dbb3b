package com.zatech.genesis.customer.portal.api.user.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true, exclude = "password")
public class PhoneRegisterRequest extends RegisterRequest {

    public static final String TYPE = "GENERAL_PHONE";

    private String countryCode;

    private String phoneNumber;

    private String password;

    public PhoneRegisterRequest() {
        super(TYPE);
    }

}
