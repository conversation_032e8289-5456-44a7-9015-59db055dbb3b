package com.zatech.genesis.customer.portal.api.payment.request;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import org.springframework.context.ApplicationEvent;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class OrderPaymentRequest extends ApplicationEvent {

    private String orderNo;

    private String paymentNo;

    private String amount;

    private String currency;

    private String status;

    private String payChannel;

    private Date payDate;

    private String paymentMethod;

    public OrderPaymentRequest(String orderNo, String paymentNo, String amount, String currency, String status, String payChannel, Date payDate, String paymentMethod) {
        super(orderNo);
        this.orderNo = orderNo;
        this.paymentNo = paymentNo;
        this.amount = amount;
        this.currency = currency;
        this.status = status;
        this.payChannel = payChannel;
        this.payDate = payDate;
        this.paymentMethod = paymentMethod;
    }

}
