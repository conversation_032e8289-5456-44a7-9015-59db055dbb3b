/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.i18n;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.zatech.octopus.component.sleuth.TraceOp;
import com.zatech.octopus.module.web.util.ApplicationContextHolderUtil;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Collection;

import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.util.StringUtils;

import static java.util.Optional.ofNullable;

@Slf4j
public class I18nSerializer extends StdSerializer<Object> implements ContextualSerializer {

    private static final String SERIALIZER_PROVIDER_ATTRIBUTE_KEY_ROOT_OBJECT = "RootObject";

    private final JsonSerializer<Object> defaultSerializer;

    public I18nSerializer(JsonSerializer<Object> defaultSerializer) {
        super(Object.class);
        this.defaultSerializer = defaultSerializer;
    }

    @Override
    public void serialize(Object value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        defaultSerializer.serialize(value, jsonGenerator, serializerProvider);
    }

    @RequiredArgsConstructor
    public class ContextValueExtractor extends JsonSerializer<Object> {

        private final I18nContextValue annotation;

        @Override
        public void serialize(Object value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            trySetRootObject(serializerProvider, annotation, value);
            defaultSerializer.serialize(value, jsonGenerator, serializerProvider);
        }

    }

    @AllArgsConstructor
    public class ActualSerializer extends JsonSerializer<Object> {

        private I18nContextValue contextAnnotation;

        private String i18nFieldName;

        private I18n annotation;

        @Override
        public void serialize(Object origin, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            // 首先检查JSON上下文，如果在数组中，说明我们被应用到了List元素上
            // 这种情况下，我们应该直接使用默认序列化器，不执行国际化逻辑
            if (isInArrayContext(jsonGenerator)) {
                defaultSerializer.serialize(origin, jsonGenerator, serializerProvider);
                return;
            }
                
            if (contextAnnotation != null) {
                trySetRootObject(serializerProvider, contextAnnotation, origin);
            }

            // 检查是否是Collection类型
            boolean isCollection = origin instanceof Collection<?>;

            // 先序列化原始值
            if (annotation.writeSelf()) {
                defaultSerializer.serialize(origin, jsonGenerator, serializerProvider);
            } else {
                jsonGenerator.writeNull();
            }

            // 如果没有国际化字段名，直接返回
            if (i18nFieldName == null) {
                return;
            }

            // 处理 List 类型字段的国际化
            if (isCollection) {
                Collection<?> collection = (Collection<?>) origin;
                processCollectionI18n(collection, jsonGenerator, serializerProvider);
                return;
            }

            // 处理单个值字段的国际化
            String value = I18nSerializer.this.getOriginValue(origin);
            if (value != null) {
                processSingleValueI18n(value, jsonGenerator, serializerProvider);
            }
        }

        private boolean isInArrayContext(JsonGenerator jsonGenerator) {
            try {
                return jsonGenerator.getOutputContext().inArray();
            } catch (Exception e) {
                return false;
            }
        }

        private void processCollectionI18n(Collection<?> collection, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            if (collection == null || collection.isEmpty()) {
                return;
            }

            I18nSerializer.this.processCollectionI18n(collection, jsonGenerator, serializerProvider, i18nFieldName, annotation);
        }

        private void processSingleValueI18n(String value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            Map<String, Object> contextValues = I18nSerializer.this.getContextValues(serializerProvider, annotation);
            String parentValue = I18nSerializer.this.getParentValue(serializerProvider, annotation);
            var language = TraceOp.getLanguage();

            String i18nValue = I18nSerializer.this.getI18nValue(value, parentValue, contextValues, language, annotation, i18nFieldName);
            safeWriteStringField(jsonGenerator, i18nFieldName, i18nValue);
        }

        @SneakyThrows
        private void safeWriteStringField(JsonGenerator jsonGenerator, String fieldName, String value) {
            jsonGenerator.writeStringField(fieldName, value);
        }

    }

    @AllArgsConstructor
    public class CollectionActualSerializer extends JsonSerializer<Object> {

        private I18nContextValue contextAnnotation;

        private String i18nFieldName;

        private I18n annotation;

        @Override
        public void serialize(Object origin, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            if (contextAnnotation != null) {
                trySetRootObject(serializerProvider, contextAnnotation, origin);
            }

            // 对于Collection字段，我们需要特殊处理
            if (origin instanceof Collection<?> collection) {
                // 先序列化原始值
                if (annotation.writeSelf()) {
                    defaultSerializer.serialize(origin, jsonGenerator, serializerProvider);
                } else {
                    jsonGenerator.writeNull();
                }

                // 如果没有国际化字段名，直接返回
                if (i18nFieldName == null) {
                    return;
                }

                // 处理Collection的国际化
                I18nSerializer.this.processCollectionI18n(collection, jsonGenerator, serializerProvider, i18nFieldName, annotation);
                return;
            }

            // 如果不是Collection（比如List中的元素），直接使用默认序列化器
            defaultSerializer.serialize(origin, jsonGenerator, serializerProvider);
        }
    }

    private void processCollectionI18n(Collection<?> collection, JsonGenerator jsonGenerator, SerializerProvider serializerProvider, String i18nFieldName, I18n annotation) throws IOException {
        if (collection == null || collection.isEmpty()) {
            return;
        }

        Map<String, Object> contextValues = getContextValues(serializerProvider, annotation);
        String parentValue = getParentValue(serializerProvider, annotation);
        var language = TraceOp.getLanguage();

        List<String> i18nValues = new ArrayList<>();
        for (Object item : collection) {
            String value = getOriginValue(item);
            if (value != null) {
                String i18nValue = getI18nValue(value, parentValue, contextValues, language, annotation, i18nFieldName);
                i18nValues.add(i18nValue);
            } else {
                i18nValues.add(null);
            }
        }

        safeWriteArrayField(jsonGenerator, i18nFieldName, i18nValues);
    }

    private Map<String, Object> getContextValues(SerializerProvider serializerProvider, I18n annotation) {
        Map<String, Object> contextValues = new HashMap<>();
        for (String contextKey : annotation.contextValueKeys()) {
            var contextValue = getRootObject(serializerProvider, contextKey);
            contextValues.put(contextKey, contextValue);
        }
        return contextValues;
    }

    private String getParentValue(SerializerProvider serializerProvider, I18n annotation) {
        if (StringUtils.hasText(annotation.parentValueFromContext())) {
            return (String) getRootObject(serializerProvider, annotation.parentValueFromContext());
        } else {
            return annotation.parentValue();
        }
    }

    private String getI18nValue(String value, String parentValue, Map<String, Object> contextValues, String language, I18n annotation, String i18nFieldName) {
        return ofNullable(ApplicationContextHolderUtil.getBean(I18nParseFactory.class))
            .flatMap(parserFactory -> parserFactory.getI18nParser(annotation.parser()))
            .map(parser -> parser.parse(new I18nParseContext()
                .setParentKey(annotation.parentKey())
                .setParentValue(parentValue)
                .setKey(annotation.key())
                .setValue(value)
                .setI18nFieldName(i18nFieldName)
                .setLanguage(language)
                .setParameters(Arrays.asList(annotation.parameters()))
                .setValues(contextValues)))
            .map(result -> ofNullable(result.i18nValue()).orElseGet(() -> {
                log.warn("No such i18n configuration (KEY: {}, VALUE: {}).", annotation.key(), value);
                return value;
            }))
            .orElse(value);
    }

    private String getOriginValue(Object origin) {
        if (origin == null) {
            return null;
        }
        if (origin instanceof Enum<?> enumValue) {
            return enumValue.name();
        }
        if (origin instanceof String stringValue) {
            return stringValue;
        }
        if (origin instanceof Boolean booleanValue) {
            return Boolean.TRUE.equals(booleanValue) ? "YES" : "NO";
        }
        return null;
    }

    private void safeWriteArrayField(JsonGenerator jsonGenerator, String fieldName, List<String> values) throws IOException {
        jsonGenerator.writeFieldName(fieldName);
        jsonGenerator.writeStartArray();
        for (String value : values) {
            if (value != null) {
                jsonGenerator.writeString(value);
            } else {
                jsonGenerator.writeNull();
            }
        }
        jsonGenerator.writeEndArray();
    }

    private Map<String, Object> getRootObjectMap(SerializerProvider provider) {
        return (Map<String, Object>) provider.getAttribute(SERIALIZER_PROVIDER_ATTRIBUTE_KEY_ROOT_OBJECT);
    }

    private void trySetRootObject(SerializerProvider provider, I18nContextValue annotation, Object object) {
        if (object == null) {
            return;
        }

        var map = getRootObjectMap(provider);
        if (map == null) {
            map = new HashMap<>();
            provider.setAttribute(SERIALIZER_PROVIDER_ATTRIBUTE_KEY_ROOT_OBJECT, map);
        }
        map.put(annotation.key(), object);
    }

    private Object getRootObject(SerializerProvider provider, String rootKey) {
        var map = getRootObjectMap(provider);
        if (map == null) {
            log.warn("No root map available, do you forget to define @I18nRoot?");
            return null;
        }
        return map.get(rootKey);
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        if (beanProperty == null) {
            return this;
        }
        var context = beanProperty.getAnnotation(I18nContextValue.class);
        var i18n = beanProperty.getAnnotation(I18n.class);
        if (i18n == null && context != null) {
            return new ContextValueExtractor(context);
        }
        if (i18n != null) {
            String fieldName = i18n.i18nFieldName();
            if (!StringUtils.hasText(fieldName)) {
                fieldName = beanProperty.getName() + i18n.i18nFieldNameSuffix();
            }
            // 检查是否是Collection类型
            if (beanProperty.getType().isCollectionLikeType()) {
                return new CollectionActualSerializer(context, fieldName, i18n);
            }
            
            return new ActualSerializer(context, fieldName, i18n);
        }
        return this;
    }

}
