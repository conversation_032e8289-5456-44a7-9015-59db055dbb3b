package com.zatech.genesis.customer.portal.api.i18n;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.NONE)
public class I18nKey {

    public static final String CURRENCY = "currencys";

    public static final String CERTIFICATION_TYPE = "certiType";

    public static final String GENDER = "gender";

    public static final String YES_NO = "yesNo";

    public static final String VEHICLE_DATABASE = "vehicleDatabase";

    public static final String RELATION = "relationship";

    public static final String PAYMENT_METHOD = "paymentMethod";

    public static final String PAYMENT_METHOD_ACCOUNT_TYPE = "paymentMethodAccountType";

    public static final String ADDRESS_16 = "address16";

    public static final String RECEIVE_INVOICE_METHOD = "receiveInvoiceMethod";

    public static final String INVOICE_TYPE = "customerInvoiceType";

    public static final String PREMIUM_DISCOUNT_TYPE = "premiumDiscountType";

    public static final String ADDITIONAL_TYPE = "additionalType";

    public static final String POS_STATUS = "posStatus";

    public static final String POS_TRANS_TYPE = "customerServiceItem";

    public static final String CLAIM_STATUS = "claimStatus";

    public static final String CLAIM_DECISION = "caseDecision";

    public static final String POS_REASON = "posReason";

    public static final String CANCELLATION_REASON = "cancellationReason";

    public static final String RELATION_INSURED = "relationshipWithMainInsured";

    public static final String APPLICATION_STATUS = "claimApplicationStatus";

}
