package com.zatech.genesis.customer.portal.api.i18n;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <NAME_EMAIL>
 * copied from pre-service
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
public @interface I18n {

    String parser() default I18nParser.METADATA;

    String parentKey() default "";

    String parentValue() default "";

    String parentValueFromContext() default "";

    String key();

    String i18nFieldName() default "";

    // if i18nFieldName(), final name of i18n field will be (original) field name + suffix
    String i18nFieldNameSuffix() default "_i18n";

    boolean writeSelf() default true;

    String[] parameters() default {};

    String[] contextValueKeys() default {};

}
