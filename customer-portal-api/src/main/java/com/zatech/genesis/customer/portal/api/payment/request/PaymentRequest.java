package com.zatech.genesis.customer.portal.api.payment.request;

import com.zatech.gaia.resource.components.enums.paymentgateway.PayChannelEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayMethodEnum;

import java.util.Map;

import lombok.Data;

@Data
public class PaymentRequest {

    private String frontendReturnUrl;

    private String frontendCancelUrl;

    private String frontendErrorUrl;

    private PayChannelEnum payChannel = PayChannelEnum.STRIPE;

    private PayMethodEnum payMethod = PayMethodEnum.CREDIT_CARD;

    private Map<String, Object> extensionParams;

}
