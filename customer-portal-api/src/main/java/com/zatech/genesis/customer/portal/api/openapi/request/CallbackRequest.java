/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.openapi.request;

import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CallbackRequest {

    private Date timestamp;

    private long messageId;

    private String version;

    private int retryTimes;

    private boolean pagination;

    private String subscription;

    private String event;

    private String bizNo;

    private JsonMap extraInfo;

    private String data;

    private String signature;

}
