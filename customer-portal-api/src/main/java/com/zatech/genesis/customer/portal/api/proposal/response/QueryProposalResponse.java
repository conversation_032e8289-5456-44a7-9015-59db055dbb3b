package com.zatech.genesis.customer.portal.api.proposal.response;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class QueryProposalResponse {

    private ProposalInfo proposalInfo;

    @Data
    @Builder
    public static class ProposalInfo {

        @Schema(title = "Issuance status")
        private IssuanceStatusEnum issuanceStatus;

        @Schema(title = "Issuance no")
        private String issuanceNo;

        @Schema(title = "Policy no")
        private String policyNo;

        private Long goodsId;

        private String goodsCode;

        @Schema(title = "Issue without payment")
        private Boolean issueWithoutPayment;

        private PolicyPremium policyPremium;

    }

    @Data
    public static class PolicyPremium {

        private CurrencyEnum baseCurrency;

        private BigDecimal basePremium;

        private CurrencyEnum premiumCurrency;

        private BigDecimal premium;

    }
}
