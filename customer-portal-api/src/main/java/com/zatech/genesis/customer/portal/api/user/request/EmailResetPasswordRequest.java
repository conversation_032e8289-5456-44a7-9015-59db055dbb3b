package com.zatech.genesis.customer.portal.api.user.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
public class EmailResetPasswordRequest extends ResetPasswordRequest {

    public static final String TYPE = "GENERAL_EMAIL";

    private String email;

    public EmailResetPasswordRequest() {
        super(TYPE);
    }

}
