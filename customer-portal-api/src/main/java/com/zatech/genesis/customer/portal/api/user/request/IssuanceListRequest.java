package com.zatech.genesis.customer.portal.api.user.request;

import com.zatech.genesis.customer.portal.api.user.base.PortalIssuanceListEnum;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/11/9
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
public class IssuanceListRequest {

    private PortalIssuanceListEnum status;

    private String issuanceNo;

}
