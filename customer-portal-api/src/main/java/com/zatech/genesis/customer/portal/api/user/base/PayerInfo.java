/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.bcp.PayMethodEnum;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.gaia.resource.components.enums.common.PayerTypeEnum;
import com.zatech.gaia.resource.components.enums.policy.InsuredStatusEnum;
import com.zatech.gaia.resource.graphene.customer.AccountTypeEnum;

import java.time.LocalDate;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Data
public class PayerInfo {

    private PayMethodEnum payMethod;

    private InsuredStatusEnum status;

    private AccountTypeEnum accountType;

    private PayerTypeEnum payerType;

    private String authAgreementNo;

    private List<AccountResp> account;

    private Long partyId;

    private Long policyPayerId;

    private Long payAccountId;

    private String fullName;

    private String firstName;

    private String middleName;

    private String lastName;

    private CertiTypeEnum certiType;

    private String certiNo;

    private LocalDate birthday;

    private GenderEnum gender;

}
