package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.graphene.customer.AccountSubTypeEnum;

import java.time.LocalDate;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Data
public class AccountResp {

    private YesNoEnum status;

    private Long payAccountId;

    private AccountSubTypeEnum accountSubType;

    private LocalDate expiryDate;

}
