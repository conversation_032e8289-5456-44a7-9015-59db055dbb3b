package com.zatech.genesis.customer.portal.api.claim.response;

import com.zatech.gaia.resource.biz.DelayTypeEnum;
import com.zatech.gaia.resource.biz.VehicleTypeEnum;
import com.zatech.gaia.resource.claim.TransportationOccurrenceTypeEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.graphene.common.CountryNationalityEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

@Data
public class DelayClaimDetail {

    private Long lossPartyId;

    /**
     * 交通工具类型：1.汽车，2.航班，3.轮船
     **/
    @Schema(description = "Transportation type")
    private VehicleTypeEnum transportationType;

    /**
     * 延误类型
     **/
    @Schema(description = "Delay type")
    private DelayTypeEnum delayType;

    /**
     * 出发时间
     **/
    @Schema(description = "Transportation date")
    private Date transportationDate;

    /**
     * 行程号
     **/
    @Schema(description = "Transportation No.")
    private String transportationNo;

    /**
     * 到达地点
     **/
    @Schema(description = "Arrival address")
    private String arrivalAddress;

    /**
     * 出发地点
     **/
    @Schema(description = "Departure address")
    private String departureAddress;

    /**
     * 到达地点code
     **/
    @Schema(description = "Arrival code")
    private String arrivalCode;

    /**
     * 出发地点code
     **/
    @Schema(description = "Departure code")
    private String departureCode;

    /**
     * 到达地点时区
     **/
    @Schema(description = "Arrival Zone ID")
    private String arrivalZoneId;

    /**
     * 出发地点时区
     **/
    @Schema(description = "Departure Zone ID")
    private String departureZoneId;

    /**
     * 交通工具实际出发时间
     **/
    @Schema(description = "Actual departure time")
    private Date actualDepartureTime;

    /**
     * 交通工具计划出发时间
     **/
    @Schema(description = "Original departure time")
    private Date originalDepartureTime;

    /**
     * 交通工具实际到达时间
     **/
    @Schema(description = "Actual arrival time")
    private Date actualArrivalTime;

    /**
     * 交通工具计划到达时间
     **/
    @Schema(description = "Original arrival time")
    private Date originalArrivalTime;

    /**
     * 到达延误时长(分钟)
     **/
    @Schema(description = "Arrival delay time")
    private Integer arrivalDelayTime;

    /**
     * 出发延误时长(分钟)
     **/
    @Schema(description = "Departure delay time")
    private Integer departureDelayTime;

    /**
     * 扩展信息
     **/
    @Schema(description = "Extension field")
    private String extraInfo;

    /**
     * 延误原因
     **/
    @Schema(description = "Delay reason")
    private String delayReason;

    @Schema(description = "Departure country")
    private CountryNationalityEnum departureCountry;

    /**
     * 发生事件类型
     **/
    @Schema(description = "Occurrence type")
    private TransportationOccurrenceTypeEnum occurrenceType = TransportationOccurrenceTypeEnum.DELAY;

    @Schema(description = "Transportation class")
    private String transportationClass;

    @Schema(description = "Arrival country")
    private CountryNationalityEnum arrivalCountry;

    @Schema(description = "Booking date")
    private Date bookingDate;

    @Schema(description = "Booking date zone Id")
    private String bookingDateZoneId;

    @Schema(description = "Change date")
    private Date changeDate;

    @Schema(description = "Change date zone Id")
    private String changeDateZoneId;

    @Schema(description = "Assessment amount")
    private String assessmentAmount;

    @Schema(description = "Booking amount")
    private String bookingAmount;

    @Schema(description = "Assessment currency")
    private CurrencyEnum assessmentCurrency;

    @Schema(description = "Booking currency")
    private CurrencyEnum bookingCurrency;

}
