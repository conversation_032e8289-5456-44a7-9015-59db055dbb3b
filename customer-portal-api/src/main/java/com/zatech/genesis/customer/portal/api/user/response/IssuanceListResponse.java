package com.zatech.genesis.customer.portal.api.user.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.gaia.resource.components.enums.product.CoveragePeriodTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
@Schema(name = "Issuance List", description = "Issuance List")
public class IssuanceListResponse {
    @Schema(title = "goodsName")
    private String goodsName;

    @Schema(title = "goodsId")
    private String goodsId;

    @Schema(title = "proposalNo")
    private String proposalNo;

    @Schema(title = "policyNo")
    private String policyNo;

    @Schema(title = "policyholderName")
    @JsonAlias("policyHolderName")
    private String policyholderName;

    @Schema(title = "premium")
    private String premium;

    @Schema(title = "commission")
    private String commission;

    @Schema(title = "hasAttachment")
    private YesNoEnum hasAttachment;

    private IssuanceStatusEnum proposalStatus;

    @JsonAlias("policyHolderType")
    @Schema(title = "policyholderType")
    private String policyholderType;

    @Schema(title = "bizApplyNo")
    private String bizApplyNo;

    @Schema(title = "transactionNo")
    private String transactionNo;

    @Schema(title = "open policy")
    private YesNoEnum openPolicy;

    @Schema(title = "relationNo")
    private String relationNo;

    @Schema(title = "currency")
    private CurrencyEnum currency;

    @Schema(title = "renewal")
    private YesNoEnum renewal;

    @Schema(title = "temporary")
    private Boolean temporary;

    @Schema(title = "isRenewalPolicy")
    private YesNoEnum isRenewalPolicy;

    @Schema(title = "insuredName")
    private String insuredName;

    @Schema(title = "effectiveDate")
    private Date effectiveDate;

    @Schema(title = "expireDate")
    private Date expireDate;

    @Schema(title = "保障期间类型:1：天，2：周，3：月，4：年，5：年满型，6：岁满型，7：终身型")
    private CoveragePeriodTypeEnum coveragePeriodType;

    @Schema(title = "保障期间")
    private String coveragePeriod;

    @Schema(title = "applicationDate")
    private Date applicationDate;

    @Schema(title = "channelRole")
    private Integer channelRole;

}
