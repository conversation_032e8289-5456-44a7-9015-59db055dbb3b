package com.zatech.genesis.customer.portal.api.user.request;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString(exclude = "password")
@RequiredArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", include = JsonTypeInfo.As.EXISTING_PROPERTY, visible = true)
@JsonSubTypes(value = {
    @JsonSubTypes.Type(value = EmailResetPasswordRequest.class, name = EmailResetPasswordRequest.TYPE),
    @JsonSubTypes.Type(value = PhoneResetPasswordRequest.class, name = PhoneResetPasswordRequest.TYPE),
})
public abstract class ResetPasswordRequest {

    private final String type;

    private String channelCode;

    private String verificationCode;

    private String nonce;

    private String password;

}
