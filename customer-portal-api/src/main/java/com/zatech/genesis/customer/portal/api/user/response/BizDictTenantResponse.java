/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.response;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.metadata.BizDictDataTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class BizDictTenantResponse {

    @Schema(name = "parentKey")
    private String parentKey;

    @Schema(name = "parentValue")
    private String parentValue;

    @Schema(name = "parentValueName")
    private String parentValueName;

    /**
     * 字典key
     */
    @Schema(title = "字典key")
    private String dictKey;

    /**
     * 字典key名称
     */
    @Schema(title = "字典key名称")
    private String dictKeyName;

    /**
     * 字典value
     */
    @Schema(title = "字典value")
    private String dictValue;

    /**
     * 字典value名称
     */
    @Schema(title = "字典value名称")
    private String dictValueName;

    /** 字典描述 */
    /**
     * @deprecated
     */
    @Deprecated
    @Schema(title = "字典描述")
    private String dictDesc;

    private String enumItemName;

    private String itemExtend1;

    private String itemExtend2;

    private String itemExtend3;

    private String itemExtend4;

    private String itemExtend5;

    private String itemExtend6;

    private YesNoEnum isOptional;

    private Integer orderNo;

    private String groupCode;

    private String groupName;

    /**
     * 子列表
     */
    @Schema(title = "子列表")
    private List<BizDictTenantResponse> childList;

    private List<BizDictItemI18nResponse> dictItemI18ns;

    private BizDictDataTypeEnum dataType;

    @Getter
    @Setter
    public static class BizDictItemI18nResponse {

        private Long id;

        private String language;

        private String value;

        private String valueName;

    }

}
