package com.zatech.genesis.customer.portal.api.payment;

import com.zatech.genesis.customer.portal.api.payment.request.PaymentRequest;
import com.zatech.genesis.customer.portal.api.payment.response.CheckPaymentInfoResponse;
import com.zatech.genesis.customer.portal.api.payment.response.PaymentResponse;
import com.zatech.genesis.customer.portal.api.payment.response.QueryPaymentInfoResponse;

import io.swagger.v3.oas.annotations.Operation;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/api/payment")
public interface PaymentApi {

    @Operation(summary = "Query Payment Info")
    @GetMapping("/{issuance-no}/check")
    @PreAuthorize("authenticate() && @checkAuth.checkIssuanceNo(#issuanceNo)")
    CheckPaymentInfoResponse check(@PathVariable("issuance-no") String issuanceNo);

    @Operation(summary = "Query Payment Info")
    @GetMapping("/{issuance-no}/{pay-order-no}/query")
    @PreAuthorize("authenticate() && @checkAuth.checkIssuanceNo(#issuanceNo)")
    QueryPaymentInfoResponse queryPayOrder(@PathVariable("issuance-no") String issuanceNo, @PathVariable("pay-order-no") String payOrderNo);

    @Operation(summary = "Query Payment Info")
    @PostMapping("/{issuance-no}/pay")
    @PreAuthorize("authenticate() && @checkAuth.checkIssuanceNo(#issuanceNo)")
    PaymentResponse pay(@PathVariable("issuance-no") String issuanceNo, @RequestBody PaymentRequest request);

}
