package com.zatech.genesis.customer.portal.api.pendingcase;

import com.zatech.genesis.customer.portal.api.pendingcase.request.SavePendingCaseFileRequest;
import com.zatech.genesis.customer.portal.api.pendingcase.response.PendingCaseResponse;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 待处理案例API接口
 * 通过JWT token中的pendingCaseNo自动识别案例
 */
@RequestMapping("/api/pending-cases")
public interface PendingCaseApi {

    /**
     * 查询待处理案例
     * pendingCaseNo从JWT token中提取
     * 
     * @return 待处理案例响应
     */
    @GetMapping
    PendingCaseResponse queryPendingCase();

    /**
     * 删除待处理案例文件
     * pendingCaseNo从JWT token中提取
     * 
     * @param pendingCaseFileId 待处理案例文件ID
     */
    @DeleteMapping("/files/{pending-case-file-id}")
    void deletePendingCase(@PathVariable(name = "pending-case-file-id") Long pendingCaseFileId);

    /**
     * 保存待处理案例文件
     * pendingCaseNo从JWT token中提取
     * 
     * @param request 保存请求
     */
    @PostMapping("/files")
    void savePendingCaseFile(@RequestBody SavePendingCaseFileRequest request);
}
