package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.common.PhoneTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Data
public class Phone {
    @Schema(title = "phone ID")
    private Long phoneId;

    @Schema(title = "if is default phone no. :1-yes;2-no")
    private YesNoEnum isDefault;

    @Schema(title = "party ID")
    private Long partyId;

    @Schema(title = "phone country code")
    private String countryCode;

    @Schema(title = "phone type")
    private PhoneTypeEnum phoneType;

    @Schema(title = "phone no")
    private String phoneNo;

    @Schema(title = "status：1-valid，2-invalid")
    private YesNoEnum status;
}
