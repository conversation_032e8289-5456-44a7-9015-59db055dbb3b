/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Setter
@Getter
@Schema(name = "File", description = "File")
@Accessors(chain = true)
public class File {

    @Schema(title = "fileUniqueCode")
    private String fileUniqueCode;

    @Schema(title = "fileName")
    private String fileName;

    @Schema(title = "fileUrl")
    private String fileUrl;

    @Schema(title = "fileType")
    private String fileType;

    @Schema(title = "date")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date date;

    @Override
    public int hashCode() {
        return 17 * 31 + (this.fileUniqueCode == null ? 0 : this.fileUniqueCode.hashCode());
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof File)) {
            return false;
        }
        File itemObj = (File) obj;
        // 地址相等
        if (this == itemObj) {
            return true;
        }
        return itemObj.fileUniqueCode.equals(this.fileUniqueCode);
    }

}
