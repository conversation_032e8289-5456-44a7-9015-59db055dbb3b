/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
package com.zatech.genesis.customer.portal.api.user.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-13 9:30
 */
@Schema(title = "Insured Object Base Info")
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class InsuredObjectBaseInfo extends ObjectCommonBase {

    /**
     * 租户定义，仅用作分类及展示，不用做业务判断（支持三层定义）
     */
    @Schema(title = "Category1", description = "The primary classification or category of an insured object or event.")
    private String category1;

    /**
     * 租户定义，仅用作分类及展示，不用做业务判断（支持三层定义）
     */
    @Schema(title = "Category2", description = "The secondary classification or category of an insured object or event.")
    private String category2;

    /**
     * 租户定义，仅用作分类及展示，不用做业务判断（支持三层定义）
     */
    @Schema(title = "Category3", description = "The tertiary classification or category of an insured object or event.")
    private String category3;

    /**
     * 投保单附件信息
     */
    @Schema(title = "Insure attachment list", description = "A list of attachments associated with an insurance issuance, such as documents or files.")
    private List<InsuredAttachment> insuredAttachmentList;
}
