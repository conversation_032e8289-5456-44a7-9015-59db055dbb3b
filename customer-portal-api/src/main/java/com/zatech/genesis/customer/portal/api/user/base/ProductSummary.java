/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.product.PayFrequencyTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/11/5
 */
@Getter
@Setter
@Schema(name = "ProductSummary", description = "ProductSummary")
public class ProductSummary {

    @Schema(title = "productDetailList")
    private List<ProductDetail> productDetailList;

    @Schema(title = "effectiveDate")
    private Date effectiveDate;

    @Schema(title = "expiryDate")
    private Date expiryDate;

    @Schema(title = "paymentFrequency")
    private PayFrequencyTypeEnum paymentFrequency;

    private CurrencyEnum currency;

    private String coveragePeriod;

    private Long policyProductId;
}
