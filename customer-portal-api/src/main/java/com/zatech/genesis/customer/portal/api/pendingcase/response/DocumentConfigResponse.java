package com.zatech.genesis.customer.portal.api.pendingcase.response;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DocumentConfigResponse {

    private String documentType;

    private boolean required;

    @Schema(description = "upload min limit")
    private Integer minPiecesRequired;

    @Schema(description = "Count of currently uploaded pieces")
    private Integer uploadedPieces;

}
