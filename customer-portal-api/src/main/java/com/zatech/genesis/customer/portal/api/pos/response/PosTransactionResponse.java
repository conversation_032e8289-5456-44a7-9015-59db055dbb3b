/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.pos.response;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyProductStatusChangeCauseEnum;
import com.zatech.gaia.resource.graphene.posonline.PosTransEffectiveDateEnum;
import com.zatech.gaia.resource.graphene.posonline.PosTransactionStatusEnum;
import com.zatech.gaia.resource.graphene.product.CancellationTypeEnum;
import com.zatech.genesis.customer.portal.api.i18n.I18n;
import com.zatech.genesis.customer.portal.api.i18n.I18nKey;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * POS transaction
 *
 * <AUTHOR> 2021-01-28 11:20
 */
@Setter
@Getter
@Schema(title = "The primary result of case' transaction")
public class PosTransactionResponse {

    /**
     * posTransactionId
     */
    @Schema(title = "The ID of transaction", hidden = true)
    private Long posTransactionId;

    /**
     * 保全项流水号
     */
    @Schema(title = "The Number of transaction")
    private String posNo;

    /**
     * 保全项类型
     */
    @Schema(title = "The  pos-item of transaction")
    @I18n(key = I18nKey.POS_TRANS_TYPE)
    private TransTypeEnum posTransType;

    /**
     * 保全项交易状态
     */
    @Schema(title = "The  status of transaction")
    private PosTransactionStatusEnum posTransStatus;

    /**
     * 生效时间
     */
    @Schema(title = "The  effective date type of transaction")
    private PosTransEffectiveDateEnum posTransEffectiveDate;

    /**
     * 生效时间
     */
    @Schema(title = "The  effective date  of transaction")
    private Date effectiveDate;

    /**
     * 保全生效日期类型
     */
    @Schema(title = "The effective type of POS transaction")
    private PosTransEffectiveDateEnum effectiveDateType;

    /**
     * 生效时区
     */
    @Schema(title = "The  effective date zone ID of transaction")
    private String effectiveZoneId;

    /**
     * 原因类型
     */
    @I18n(key = I18nKey.POS_REASON)
    @Schema(title = "The  reason code of transaction")
    private String reasonCode;

    /**
     * 原因
     */
    @Schema(title = "The   reason detail of transaction")
    private String reason;

    /**
     * 用户实际请求时间
     */
    @Schema(title = "The actual date of user request")
    private Date requestDate;

    /**
     * 取消类型
     */
    @Schema(title = "The type of cancellation")
    private CancellationTypeEnum cancellationType;

    @I18n(key = I18nKey.CANCELLATION_REASON)
    @Schema(title = "The type of cancellation")
    private PolicyProductStatusChangeCauseEnum cancellationReason;

}
