package com.zatech.genesis.customer.portal.api.market;

import com.zatech.genesis.customer.portal.api.market.request.QueryGoodsRequest;
import com.zatech.genesis.customer.portal.api.market.response.GoodsResponse;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @create 2025/3/13 14:50
 */
@Tag(name = "User's Market Domain Resource API")
@RequestMapping(path = "/api/users")
public interface MarketApiResource {

    @PostMapping(value = "/goods")
    @PreAuthorize("authenticate()")
    GoodsResponse queryGoodsRelating(@RequestBody QueryGoodsRequest request);
}
