package com.zatech.genesis.customer.portal.api.pendingcase.response;

import com.zatech.gaia.resource.pendingcase.PendingCaseStatusEnum;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BusinessInfoResponse {

    private PendingCaseStatusEnum pendingCaseStatus;

    private String policyNo;

    private String businessModule;

    private String claimCaseNo;

    private String applicationNo;

    private String pendingCaseNo;


}
