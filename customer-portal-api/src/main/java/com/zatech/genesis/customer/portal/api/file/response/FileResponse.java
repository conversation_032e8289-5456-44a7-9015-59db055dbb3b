package com.zatech.genesis.customer.portal.api.file.response;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class FileResponse {

    @Schema(name = "file code")
    private String fileUniqueCode;

    @Schema(name = "file path")
    private String fileInsideUrl;

    @Schema(name = "file name")
    private String fileName;

    @Schema(name = "file format")
    private String fileFormat;

}
