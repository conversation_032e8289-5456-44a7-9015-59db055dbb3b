package com.zatech.genesis.customer.portal.api.user.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/11/1
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
public class ChangePasswordRequest {

    @NotBlank(message = "{verification.code.NotBlank.message}")
    private String verificationCode;

    @NotBlank(message = "{verification.nonce.NotBlank.message}")
    private String nonce;

    private String originalPassword;

    private String newPassword;

}
