/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.request;

import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.gaia.resource.components.enums.schema.OrganizationIDTypeEnum;

import java.time.LocalDate;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
public class QueryUserIdentifyRequest {

    private PartyTypeEnum customerType = PartyTypeEnum.INDIVIDUAL;

    private Long channelCustomerId;

    private List<String> emailList;

    private String certiNo;

    private CertiTypeEnum certiType;

    private String fullName;

    private String fullName2;

    private String fullName3;

    private LocalDate birthday;

    private GenderEnum gender;

    private List<String> zipCodeList;

    private String firstName;

    private String middleName;

    private String lastName;

    private String organizationIdNo;

    private OrganizationIDTypeEnum organizationIdType;

}