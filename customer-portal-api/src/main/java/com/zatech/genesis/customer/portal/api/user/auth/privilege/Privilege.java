package com.zatech.genesis.customer.portal.api.user.auth.privilege;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zatech.genesis.customer.portal.api.user.auth.permission.Permission;

import org.springframework.security.core.GrantedAuthority;

/**
 * <AUTHOR>
 * @date 2023/10/31
 */
public interface Privilege extends GrantedAuthority {

    boolean isGrantedOf(Permission permission);

    @JsonIgnore
    @Override
    default String getAuthority() {
        return null;
    }
}
