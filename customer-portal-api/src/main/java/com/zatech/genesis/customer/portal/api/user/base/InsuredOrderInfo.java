/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Schema(name = "InsuredOrderInfo", description = "InsuredOrderInfo")
@Getter
@Setter
@Accessors(chain = true)
public class InsuredOrderInfo extends InsuredObjectBaseInfo {
    @Schema(
            title = "Platform order number"
    )
    private String bookingNumber;

    @Schema(
            title = "Order number"
    )
    private String orderNumber;

    @Schema(
            title = "Order currency"
    )
    private CurrencyEnum orderCurrency;

    @Schema(
            title = "Order price"
    )
    private String orderPrice;

    @Schema(
            title = "Effective date"
    )
    private Date effectiveDate;

    @Schema(
            title = "Effective date time zone"
    )
    private String effectiveDateTimeZone;

    @Schema(
            title = "Expiry date"
    )
    private Date expiryDate;

    @Schema(
            title = "Expiry date time zone"
    )
    private String expiryDateTimeZone;

    @Schema(
            title = "Event Code"
    )
    private String eventCode;

    @Schema(title = "Number of order", description = "original type is Integer, in order to front end display, convert to String")
    private String numberOfOrder;

    @Schema(title = "Order type")
    private String orderType;

    @Schema(
            title = "Order date"
    )
    private Date orderDate;

    @Schema(
            title = "Category1"
    )
    private String category1;

    @Schema(
            title = "Category2"
    )
    private String category2;

    @Schema(
            title = "Category3"
    )
    private String category3;

    @Schema(
            title = "扩展字段集合",
            description = "动态添加扩展字段,Map.Entry.Key与Map.Entry.Value目前只支持String"
    )
    private Map<String, String> extensions;
    
}
