/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user;

import com.zatech.genesis.customer.portal.api.user.request.LoginRequest;
import com.zatech.genesis.customer.portal.api.user.request.RegisterRequest;
import com.zatech.genesis.customer.portal.api.user.request.ResetPasswordRequest;
import com.zatech.genesis.customer.portal.api.user.request.SendVerificationCodeRequest;
import com.zatech.genesis.customer.portal.api.user.request.ValidateVerificationCodeRequest;
import com.zatech.genesis.customer.portal.api.user.response.LoginResponse;
import com.zatech.genesis.customer.portal.api.user.response.RegisterResponse;
import com.zatech.genesis.customer.portal.api.user.response.SendVerificationCodeResponse;
import com.zatech.genesis.customer.portal.api.user.response.ValidateVerificationCodeResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.core.io.InputStreamResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * <AUTHOR>
 * @create 2023/9/21 17:44
 **/
@Deprecated
@Tag(name = "User Auth Resource API")
@RequestMapping(path = "/api/users/operate")
public interface UserAuthApiResource {

    @Deprecated
    @Operation(summary = "Generate Captcha")
    @GetMapping(params = "captcha", produces = MediaType.IMAGE_JPEG_VALUE)
    @ResponseBody
    ResponseEntity<InputStreamResource> generateCaptcha(@RequestParam("nonce") String nonce);

    @Deprecated
    @Operation(summary = "Send Verification Code")
    @PostMapping(params = "code", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    SendVerificationCodeResponse sendVerificationCode(@RequestBody SendVerificationCodeRequest request);

    @Deprecated
    @Operation(summary = "Register")
    @PostMapping(consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    RegisterResponse register(@RequestBody RegisterRequest request);

    @Deprecated
    @Operation(summary = "Login")
    @PostMapping(params = "login", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    LoginResponse login(@RequestBody @Valid LoginRequest request);

    @Deprecated
    @Operation(summary = "Register And Login")
    @PostMapping(params = "register-login", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    LoginResponse registerAndLogin(@RequestBody RegisterRequest request);

    @Deprecated
    @Operation(summary = "Reset Password")
    @PostMapping(params = "reset-password", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    void resetPassword(@RequestBody ResetPasswordRequest request);

    @Deprecated
    @Operation(summary = "Graphic captcha switch")
    @GetMapping("/captcha-switch")
    boolean captchaSwitch();

    @Deprecated
    @Operation(summary = "Validate verification code")
    @PostMapping(value = "/validate-otp")
    ValidateVerificationCodeResponse validate(@RequestBody ValidateVerificationCodeRequest request);

}
