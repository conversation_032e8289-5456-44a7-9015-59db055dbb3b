package com.zatech.genesis.customer.portal.api.user.response;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/11/10
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
public class SchemaDefUniqueKeyResponse {

    @Schema(title = "schema def type. ref. SchemaDefTypeEnum")
    private Integer schemaDefType;

    @Schema(title = "id of specific schema def table")
    private Long schemaDefRefId;

    @Schema(title = " customer type. ref. CustomerTypeEnum")
    private Integer customerType;

    @Schema(title = " object category. ref. ObjectCategoryEnum")
    private Integer objectCategory;

    @Schema(title = "object detail types. ref. ObjectSubCategoryEnum")
    private Integer objectSubCategory;

    @Schema(title = "order no")
    private Integer orderNo;

    @Schema(title = "field Code")
    private String fieldCode;

}
