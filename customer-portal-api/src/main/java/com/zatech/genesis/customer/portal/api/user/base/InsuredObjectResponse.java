/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
package com.zatech.genesis.customer.portal.api.user.base;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Schema(title = "Proposal insured object response")
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class InsuredObjectResponse extends InsuredObjectBaseInfo {
    
    @Schema(title = "insured order")
    private Object insuredOrderInfo;

    @Schema(title = "travel order")
    private Object insuredTravelInfo;

    @Schema(title = "vehicle object")
    private Object insuredVehicleInfo;

    @Schema(title = "vehicle object")
    private Object insuredDeviceInfo;

    @Schema(title = "insured name", description = "The name of the insured individual or entity under a policy.")
    private String insuredName;

}
