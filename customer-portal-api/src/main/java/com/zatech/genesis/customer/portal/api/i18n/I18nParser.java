/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.i18n;

public interface I18nParser {

    String UNDERSCORE_TO_SPACE_AND_CAPITALIZE_INITIAL_LETTER = "underscoreToSpaceAndCapitalizeInitialLetter";

    String LOCAL_FILE = "localFile";

    String METADATA = "metadata";

    String CAMPAIGN = "campaignName";

    String APPLICATION_ELEMENT = "applicationElement";

    String APPLICATION_ELEMENT_OBJECT = "object";

    String APPLICATION_ELEMENT_HOLDER = "holder";

    String APPLICATION_ELEMENT_PAYER = "payer";

    record Result(String i18nFieldName, String i18nValue) { }

    Result parse(I18nParseContext context);

}
