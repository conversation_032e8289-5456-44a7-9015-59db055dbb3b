package com.zatech.genesis.customer.portal.api.payment.response;

import com.zatech.genesis.customer.portal.api.payment.enums.PayStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class PaymentResponse {

    @Schema(name = "pay status")
    private PayStatusEnum status;

    @Schema(name = "third order no")
    private String thirdPartyOrderNo;

    @Schema(name = "redirect url")
    private String redirectUrl;

    @Schema(name = "pay date")
    private Date payDate;

    @Schema(name = "form param, use to redirectUrl")
    private Map<String, Object> formParams = new HashMap<>();

    public void addParams(Map<String, Object> addParams) {
        formParams.putAll(addParams);
    }

}
