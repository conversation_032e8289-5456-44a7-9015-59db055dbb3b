/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.policy.PolicySpecialAgreementTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/11/5
 */
@Setter
@Getter
@Schema(name = "PurchaseSummary", description = "PurchaseSummary")
public class SpecialAgreement {

    @Schema(title = "Policy special agreement code")
    private String specialAgreementCode;

    @Schema(title = "Policy special agreement description")
    private String specialAgreementDescription;

    @Schema(title = "Policy special agreement type")
    private PolicySpecialAgreementTypeEnum specialAgreementType;

    @Schema(title = "Order number")
    private Long orderNumber;
}
