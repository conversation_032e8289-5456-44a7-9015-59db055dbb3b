/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.pos.response;

import com.zatech.gaia.resource.components.enums.posonline.PosStatusEnum;
import com.zatech.gaia.resource.graphene.posonline.PosCaseSourceEnum;
import com.zatech.genesis.customer.portal.api.i18n.I18n;
import com.zatech.genesis.customer.portal.api.i18n.I18nKey;
import com.zatech.genesis.customer.portal.api.i18n.I18nParser;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR> 2021-01-27 14:54
 */
@Getter
@Setter
@Schema(title = "The result of case query")
public class PosQueryListResultResponse {

    /**
     * Case No
     */
    @Schema(title = "The number name of case")
    private String caseNo;

    /**
     * 保单号
     */
    @Schema(title = "The number name of policy")
    private String policyNo;

    /**
     * 触发点
     */
    @Schema(title = "The trigger source,such as POS_MANUAL or  CHANNEL_API")
    private PosCaseSourceEnum caseSource;


    /**
     * 保全状态
     */
    @Schema(title = "The  status of case")
    @I18n(parser = I18nParser.LOCAL_FILE, key = I18nKey.POS_STATUS)
    private PosStatusEnum caseStatus;

    private Date applicationDate;

    private String productName;

    /**
     * 保全e
     */
    @Schema(title = "The  aggregated result of transaction")
    private List<PosTransactionResponse> transactions;

    /**
     * 申请号
     */
    @Schema(title = "The number of application from other server")
    private String thirdPartyTransactionNo;

    private boolean needUploadDocument;

    public Date getRequestDate() {

        return transactions.stream().findFirst().orElseThrow().getRequestDate();
    }

}
