package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.customer.label.LabelActionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Map;

@Schema(title = "Label", description = "Label")
@Setter
@Getter
@Accessors(chain = true)
public class Label {
    @Schema(
            title = "Action type"
    )
    private LabelActionType actionType;
    @Schema(
            title = "Label id"
    )
    private Long labelId;
    @Schema(
            title = "Code"
    )
    private String code;
    @Schema(
            title = "Name"
    )
    private String name;
    @Schema(
            title = "Person id"
    )
    private Long personId;
    @Schema(
            title = "Party ID"
    )
    private Long partyId;
    @Schema(
            title = "Is deleted"
    )
    private YesNoEnum isDeleted;
    @Schema(
            title = "Extra info field"
    )
    private Map<String, String> extraInfoField;
    @Schema(
            title = "Extra info"
    )
    private String extraInfo;
    @Schema(
            title = "Remark"
    )
    private String remark;
}
