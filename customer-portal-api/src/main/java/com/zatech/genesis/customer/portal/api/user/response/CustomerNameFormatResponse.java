package com.zatech.genesis.customer.portal.api.user.response;

import com.zatech.genesis.customer.portal.api.user.base.CustomerNameFormatGroupResponse;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/11/10
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
public class CustomerNameFormatResponse {

    private CustomerNameFormatGroupResponse name1;

    private CustomerNameFormatGroupResponse name2;

    private CustomerNameFormatGroupResponse name3;

}
