/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.response;

import com.zatech.gaia.resource.components.enums.policy.PolicyFileSourceTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Data;

/**
 * Description: #GIS-119265 Adding clause file information for the policy.
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/13 11:49
 */
@Data
public class PolicyClauseFileResponse implements Serializable {

    private static final long serialVersionUID = -3477966697159611957L;

    @Schema(title = "File Path", description = "This is a URL path that accesses it.")
    private String filePath;

    @Schema(title = "File unique code", description = "The unique code assigned to a file or document related to an insurance claim or policy.")
    private String fileUniqueCode;

    @Schema(title = "Doc name", description = "The name of the document associated with the policy.")
    private String docName;

    @Schema(title = "Doc type", description = "The type of document associated with a policy or transaction.")
    private String docType;

    @Schema(title = "Source type", description = "The type of source (e.g., internal, external) for the policy or event.")
    private PolicyFileSourceTypeEnum sourceType;

    @Schema(title = "sourceType: product - product_id, goods - goods_id", description = "The unique identifier of the source of the policy or event.")
    private Long sourceId;

    @Schema(title = "Version", description = "The version of the policy or record in an insurance policy.")
    private String version;

    @Schema(title = "Language", description = "The language used for a specific field or entity.")
    private String language;

}
