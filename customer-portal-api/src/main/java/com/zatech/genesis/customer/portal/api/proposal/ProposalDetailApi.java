package com.zatech.genesis.customer.portal.api.proposal;

import com.zatech.genesis.customer.portal.api.proposal.response.QueryProposalResponse;

import io.swagger.v3.oas.annotations.Operation;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

public interface ProposalDetailApi {

    @Operation(summary = "Query proposal info")
    @GetMapping(value = "/api/proposals/{proposal-no}/info")
    @PreAuthorize("authenticate() && @checkAuth.checkIssuanceNo(#issuanceNo)")
    QueryProposalResponse queryProposal(@PathVariable("proposal-no") String proposalNo);

}
