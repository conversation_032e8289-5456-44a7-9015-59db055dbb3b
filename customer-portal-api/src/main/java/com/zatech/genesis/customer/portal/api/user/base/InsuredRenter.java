/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
package com.zatech.genesis.customer.portal.api.user.base;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.zatech.gaia.resource.components.enums.common.RelationEnum;
import com.zatech.gaia.resource.components.enums.common.UserTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-03-13 9:31
 */
@Schema(name = "Insured Renter", description = "Insured Renter")
@Getter
@Setter
@Accessors(chain = true)
public class InsuredRenter extends ObjectCommonBase {

    /**
     * 用户类型1：个人，2：公司
     */
    @Schema(title = "User type", description = "The type of user (e.g., Policyholder, Insured, Agent).")
    private UserTypeEnum userType;

    /**
     * 与持有人关系
     */
    @Schema(title = "Relationship with policyholder", description = "The relationship of the insured to the policyholder (e.g., Self, Spouse, Child).")
    @JsonAlias({"holderRelation", "relationshipWithPolicyholder"})
    private RelationEnum relationshipWithPolicyholder;

    @Schema(title = "Party type", description = "The type of party (e.g., Individual, Organization).")
    private PartyTypeEnum partyType;




}
