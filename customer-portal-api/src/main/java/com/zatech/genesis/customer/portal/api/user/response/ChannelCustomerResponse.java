/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.response;

import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.genesis.customer.portal.api.user.base.BaseCustomerComponentDTO;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
@ToString
public class ChannelCustomerResponse extends BaseCustomerComponentDTO {

    @Schema(title = "channelCode")
    private String channelCode;

    @Schema(title = "Login type")
    private String loginType;

    private PartyTypeEnum customerType;

}
