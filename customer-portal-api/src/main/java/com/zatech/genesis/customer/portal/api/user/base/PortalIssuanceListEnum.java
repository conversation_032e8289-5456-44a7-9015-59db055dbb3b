package com.zatech.genesis.customer.portal.api.user.base;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/11/13
 */
public enum PortalIssuanceListEnum {
    ONGOING("ongoing", "11,1", "application in progress"),
    PENDING("pending", "12,2", "waiting for issuance, waiting for review"),
    COMPLETED("completed", "3,9,10", "rejected,effective,withdrawn");

    private String code;

    private String statusMapping;

    private String desc;

    PortalIssuanceListEnum(String code, String statusMapping, String desc) {
        this.code = code;
        this.statusMapping = statusMapping;
        this.desc = desc;
    }

    public static Set<Integer> getStatusMapping(PortalIssuanceListEnum status) {
        if (status == null) {
            return new HashSet<>();
        }
        return new HashSet<>(Arrays.stream(status.statusMapping.split(",")).map(Integer::parseInt).toList());
    }

}
