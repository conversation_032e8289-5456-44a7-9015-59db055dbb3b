package com.zatech.genesis.customer.portal.api.user.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
public class ChangeMobileRequest {

    private PhoneRequest phone;

    private String verificationCode;

    @NotBlank(message = "{verification.nonce.NotBlank.message}")
    private String nonce;


    @Getter
    @Setter
    public static class PhoneRequest {

        private String countryCode;

        private String number;

    }


}
