package com.zatech.genesis.customer.portal.api.market.response;

import com.zatech.gaia.resource.biz.ScopeEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyScenarioEnum;

import lombok.Data;

@Data
public class PackageInfoResponse {

    private Long packageId;

    private PackageBasicInfoResponse packageBasicInfo;

    @Data
    public static class PackageBasicInfoResponse {

        private Long packageId;

        private String packageCode;

        private String packageName;

        private String packageRemark;

        private Long categoryId;

        private Integer packageStatus;

        private String creator;

        private Boolean readOnly;

        private ScopeEnum scope;

        private PolicyScenarioEnum policyScenario;
    }

}
