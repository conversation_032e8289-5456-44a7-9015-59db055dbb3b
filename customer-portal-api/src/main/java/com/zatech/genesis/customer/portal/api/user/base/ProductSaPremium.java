/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zatech.genesis.customer.portal.api.user.util.DecimalSerializer;

import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ProductSaPremium {

    private String productName;

    private String productCode;

    private Long productId;

    /**
     * product总保额
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal sumInsured;

    /**
     * product 标准保额
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal standardPremium;

    /**
     * 分期 - 总保费
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalFinalPremium;

    /**
     * 期缴最终总费用
     * periodFinalPremium = periodStandardPremium
     * + periodExtraPremium
     * - periodDiscountPremium
     * + periodTotalTax
     * + periodLevy
     * + totalServiceFee
     * + totalCommissionFee
     * - periodCampaignDiscount
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodFinalPremium;

    /**
     * Insurance Tax
     * the total tax of coverage total
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalTax;

    /**
     * 总NCD
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalNoClaimDiscount;

    /**
     * Total Premium Before Tax
     * the net premium of coverage total
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal coverageTotalNetPremium;

    /**
     * periodNoClaimDiscount
     */
    @JsonSerialize(using = DecimalSerializer.class)
    private BigDecimal periodNoClaimDiscount;

    /**
     * 加费信息
     */
    private List<ProductExtraPremiumOutput> extraPremiumDetails;

    /**
     * 折扣信息
     */
    private List<ProductDiscountPremiumOutput> discountPremiumDetails;

    private Boolean isMainProduct;
}
