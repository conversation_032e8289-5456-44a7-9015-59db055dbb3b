package com.zatech.genesis.customer.portal.api.user;

import com.zatech.genesis.customer.portal.api.user.request.IssuanceListRequest;
import com.zatech.genesis.customer.portal.api.user.request.PolicyListRequest;
import com.zatech.genesis.customer.portal.api.user.response.IssuanceDetailResponse;
import com.zatech.genesis.customer.portal.api.user.response.IssuanceListResponse;
import com.zatech.genesis.customer.portal.api.user.response.PolicyDetailResponse;
import com.zatech.genesis.customer.portal.api.user.response.PolicyListResponse;
import com.zatech.genesis.customer.portal.share.PageRequest;
import com.zatech.octopus.common.dao.Page;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Tag(name = "User's Policy Domain Resource API")
@RequestMapping(path = "/api/users/policy")
public interface UserPolicyApiResource {

    @Operation(summary = "Get Login User's Policy List")
    @PreAuthorize("authenticate()")
    @PostMapping("/policies")
    @ResponseBody
    Page<PolicyListResponse> queryPolicyList(@RequestBody PageRequest<PolicyListRequest> requestPage);

    @Operation(summary = "Get Login User's Policy detail")
    @PreAuthorize("authenticate() && @checkAuth.checkPolicyNo(#policyNo)")
    @GetMapping(value = "/{policy-no}/info", produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    PolicyDetailResponse queryPolicyDetail(@PathVariable("policy-no") String policyNo);

    @Operation(summary = "Get Login User's issuance List")
    @PreAuthorize("authenticate()")
    @PostMapping("/issuances")
    @ResponseBody
    Page<IssuanceListResponse> queryIssuanceList(@RequestBody PageRequest<IssuanceListRequest> requestPage);

    @Operation(summary = "Get Login User's issuance detail")
    @PreAuthorize("authenticate() && @checkAuth.checkIssuanceNo(#issuanceNo)")
    @GetMapping("/issuance/{issuance-no}/info")
    @ResponseBody
    IssuanceDetailResponse queryIssuanceDetail(@PathVariable("issuance-no") String issuanceNo);
}
