package com.zatech.genesis.customer.portal.api.user;

import com.zatech.gaia.resource.components.enums.doc.DocTypeEnum;
import com.zatech.genesis.customer.portal.api.user.request.IssuanceListRequest;
import com.zatech.genesis.customer.portal.api.user.request.PolicyListRequest;
import com.zatech.genesis.customer.portal.api.user.response.IssuanceDetailResponse;
import com.zatech.genesis.customer.portal.api.user.response.IssuanceListResponse;
import com.zatech.genesis.customer.portal.api.user.response.PolicyDetailResponse;
import com.zatech.genesis.customer.portal.api.user.response.PolicyListResponse;
import com.zatech.genesis.customer.portal.api.user.response.PolicyPosItemsResponse;
import com.zatech.genesis.customer.portal.share.PageRequest;
import com.zatech.octopus.common.dao.Page;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * <AUTHOR>
 * @create 2023/11/13 18:06
 **/
@Tag(name = "User's Policy Domain Resource API")
@RequestMapping(path = "/api/users")
public interface UserPolicyApiResourceV2 {

    @Operation(summary = "Get Login User's issuance detail")
    @PreAuthorize("authenticate() && @checkAuth.checkIssuanceNo(#issuanceNo)")
    @GetMapping("/issuances/{issuance-no}/info")
    @ResponseBody
    IssuanceDetailResponse queryIssuanceDetail(@PathVariable("issuance-no") String issuanceNo, @RequestParam("temporary") Boolean temporary);

    @Operation(summary = "Get Login User's issuance List")
    @PreAuthorize("authenticate()")
    @PostMapping("/issuances")
    @ResponseBody
    Page<IssuanceListResponse> queryIssuanceList(@RequestBody PageRequest<IssuanceListRequest> requestPage);

    @Operation(summary = "Get Login User's Policy detail")
    @PreAuthorize("authenticate() && @checkAuth.checkPolicyNo(#policyNo)")
    @GetMapping(value = "/policies/{policy-no}/info", produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    PolicyDetailResponse queryPolicyDetail(@PathVariable("policy-no") String policyNo);

    @Operation(summary = "Get Login User's Policy List")
    @PreAuthorize("authenticate()")
    @PostMapping("/policies")
    @ResponseBody
    Page<PolicyListResponse> queryPolicyList(@RequestBody PageRequest<PolicyListRequest> requestPage);

    @Operation(summary = "Get policy pos items")
    @PreAuthorize("authenticate() && @checkAuth.checkPolicyNo(#policyNo)")
    @GetMapping(value = "/policies/{policy-no}/pos-items", produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    List<PolicyPosItemsResponse> queryPolicyPosItems(@PathVariable("policy-no") String policyNo);

    @Operation(summary = "Policy E-policy download")
    @GetMapping("/policies/{policy-no}/print/document")
    @PreAuthorize("authenticate() && @checkAuth.checkPolicyNo(#policyNo)")
    void printPolicyDocument(@PathVariable("policy-no") String policyNo,
                             @RequestParam(name = "docType") DocTypeEnum docType);

}
