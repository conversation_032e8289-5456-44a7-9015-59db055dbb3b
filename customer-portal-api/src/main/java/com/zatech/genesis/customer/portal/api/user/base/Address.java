/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.common.AddressTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.CompanyAddressTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Setter
@Getter
@Accessors(chain = true)
public class Address {

    private Long addressId;

    private AddressTypeEnum addressType;

    @Schema(title = "companyAddressType")
    private CompanyAddressTypeEnum companyAddressType;

    @Schema(title = "address11")
    private String address11;

    @Schema(title = "address12")
    private String address12;

    @Schema(title = "address13")
    private String address13;

    @Schema(title = "address14")
    private String address14;

    @Schema(title = "address15")
    private String address15;

    @Schema(title = "address15")
    private String address16;

    @Schema(title = "address21")
    private String address21;

    @Schema(title = "address22")
    private String address22;

    @Schema(title = "address23")
    private String address23;

    @Schema(title = "address24")
    private String address24;

    @Schema(title = "address25")
    private String address25;

    @Schema(title = "zipCode")
    private String zipCode;

    @Schema(title = "地震CODE")
    private String jisCode;

}
