package com.zatech.genesis.customer.portal.api.claim.response;

import com.zatech.gaia.resource.claim.CaseDecisionEnum;
import com.zatech.gaia.resource.claim.ClaimStatusEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.genesis.customer.portal.api.i18n.I18n;
import com.zatech.genesis.customer.portal.api.i18n.I18nKey;

import java.util.Date;

import lombok.Data;

@Data
public class ClaimApplicationCaseResponse {

    private String policyNo;

    private String caseNo;

    private String applicationNo;

    private Date applicationDate;

    @I18n(key = I18nKey.CLAIM_STATUS)
    private ClaimStatusEnum caseStatus;

    @I18n(key = I18nKey.APPLICATION_STATUS)
    private ClaimStatusEnum applicationStatus;

    @I18n(key = I18nKey.CLAIM_DECISION)
    private CaseDecisionEnum decision;

    private Date incidentDate;

    private String payoutAmount;

    private String productName;

    @I18n(key = I18nKey.CURRENCY)
    private CurrencyEnum baseCurrency;

    private Boolean documentEnable;

    private String orderNo;

}
