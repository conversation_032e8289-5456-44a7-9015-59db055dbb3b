/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.response;

import com.zatech.gaia.resource.components.enums.product.CoveragePeriodTypeEnum;
import com.zatech.gaia.resource.components.enums.product.PayFrequencyTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Author: weizhen.kong
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
@Schema(name = "policy detail", description = "policy base response")
public class PolicyBaseResponse {

    @Schema(title = "保障期间类型:1：天，2：周，3：月，4：年，5：年满型，6：岁满型，7：终身型")
    private CoveragePeriodTypeEnum coveragePeriodType;

    @Schema(title = "coveragePeriodTypeI18n")
    private String coveragePeriodTypeI18n;

    @Schema(title = "paymentFrequency")
    private PayFrequencyTypeEnum paymentFrequency;

    @Schema(title = "paymentFrequencyI18n")
    private String paymentFrequencyI18n;

}