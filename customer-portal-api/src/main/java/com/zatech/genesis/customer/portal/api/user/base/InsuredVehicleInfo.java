/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
package com.zatech.genesis.customer.portal.api.user.base;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.zatech.gaia.resource.biz.VehicleDamageTypeEnum;
import com.zatech.gaia.resource.biz.VehiclePartEnum;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.gaia.resource.components.enums.common.MaritalStatusEnum;
import com.zatech.gaia.resource.components.enums.common.RelationEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.schema.ObjectType;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Schema(name = "InsuredVehicleInfo", description = "InsuredVehicleInfo")
@Getter
@Setter
@Accessors(chain = true)
public class InsuredVehicleInfo extends InsuredObjectBaseInfo {
    
    /**
     * 司机信息
     */
    @Schema(title = "insured auto driver list", description = "The list of drivers associated with the insured vehicle in the issuance of a policy.")
    private List<InsuredAutoDriverResponse> insuredAutoDriverList;
    
    /**
     * 勘探信息
     */
    @Schema(title = "insured auto inspection list", description = "The list of auto inspections associated with the insured vehicle in the issuance of a policy.")
    private List<InsuredAutoInspectionResponse> insuredAutoInspectionList;
    
    /**
     * 附加设备信息
     */
    @Schema(title = "Auto additional equipment")
    private List<AdditionalEquipmentResponse> additionalEquipmentList;
    
    /**
     * NCD折扣或者加费比例
     */
    @Schema(title = "NCD percent", description = "The percentage representing the No Claim Discount (NCD) level. This coefficient is used to calculate the NCD amount.")
    private String ncdCoefficient;
    
    /**
     * ncd等级
     */
    @Schema(title = "NCD scenario", description = "The code identifying the specific bonus-malus scenario applied to the policyholder. This scenario determines how is earned and lost.")
    private String bonusMalusScenario;
    
    /**
     * 车号
     */
    @Schema(title = "Vehicle no", requiredMode = RequiredMode.REQUIRED, description = "The unique identifier for a vehicle.")
    private String vehicleNo;
    
    /**
     * 型号
     */
    @Schema(title = "Model", requiredMode = RequiredMode.REQUIRED, description = "The model or version of a device or equipment being insured.")
    private String model;
    
    /**
     * 制造商
     */
    @Schema(title = "Make", requiredMode = RequiredMode.REQUIRED, description = "The make or brand of the insured vehicle in an auto insurance policy.")
    private String make;
    
    /**
     * 车型
     */
    @Schema(title = "Body type", requiredMode = RequiredMode.REQUIRED, description = "The body type of the insured vehicle in an auto insurance policy.")
    private String bodyType;
    
    /**
     * 出厂日期
     */
    @Schema(title = "Year of make", requiredMode = RequiredMode.REQUIRED, description = "The year of make of the insured vehicle in an auto insurance policy.")
    private Date yearOfMake;
    
    /**
     * 载客
     */
    @Schema(title = "Seating capity", requiredMode = RequiredMode.REQUIRED, description = "The seating capacity of the insured vehicle in an auto insurance policy.")
    private String seatingCapity;
    
    /**
     * 载重(KG)
     */
    @Schema(title = "Carring capacity", description = "The carrying capacity of the insured vehicle in an auto insurance policy.")
    private String carringCapacity;
    
    /**
     * 购买日期
     */
    @Schema(title = "Date of purchase", requiredMode = RequiredMode.REQUIRED, description = "The date of purchase of the insured vehicle in an auto insurance policy.")
    private Date dateOfPurchase;
    
    /**
     * 登记卡号
     */
    @Schema(title = "Registration card no", description = "The unique number assigned to the vehicle's registration card in an auto insurance policy.")
    private String registrationCardNo;
    
    /**
     * 发动机容量/排量
     */
    @Schema(title = "Engine capacity", requiredMode = RequiredMode.REQUIRED, description = "The engine capacity of the insured vehicle in an auto insurance policy.")
    private String engineCapacity;
    
    /**
     * 车辆颜色
     */
    @Schema(title = "Colour of vehicle", description = "The color of the insured vehicle in an auto insurance policy.")
    private String colourOfVehicle;
    
    /**
     * 使用的燃料类型
     */
    @Schema(title = "Type of fuel used", description = "The type of fuel used by the insured vehicle in an auto insurance policy.")
    private String typeOfFuelUsed;
    
    /**
     * 齿轮或转向锁
     */
    @Schema(title = "Gear or steering lock", description = "A flag indicating whether the vehicle has a gear or steering lock in an auto insurance policy.")
    private String gearOrSteeringLock;
    
    /**
     * gps
     */
    @Schema(title = "GPS", description = "A flag indicating whether the vehicle has a GPS system in an auto insurance policy.")
    private String gps;
    
    /**
     * 跟踪系统
     */
    @Schema(title = "Tracking system", description = "A flag indicating whether the vehicle has a tracking system in an auto insurance policy.")
    private String trackingSystem;
    
    /**
     * 安全气囊
     */
    @Schema(title = "Airbags", description = "The number of airbags in the insured vehicle in an auto insurance policy.")
    private String airbags;
    
    /**
     * 锁定
     */
    @Schema(title = "Immobiliser", description = "A flag indicating whether the vehicle has an immobiliser in an auto insurance policy.")
    private String immobiliser;
    
    /**
     * 报警器
     */
    @Schema(title = "Factory fitted alarm", description = "A flag indicating whether the vehicle has a factory-fitted alarm in an auto insurance policy.")
    private String factoryFittedAlarm;
    
    /**
     * abs
     */
    @Schema(title = "ABS", description = "A flag indicating whether the vehicle has an Anti-lock Braking System (ABS) in an auto insurance policy.")
    private String abs;
    
    /**
     * 用途
     */
    @Schema(title = "Use of vehicle", requiredMode = RequiredMode.REQUIRED, description = "The use of the insured vehicle in an auto insurance policy, such as personal or commercial.")
    private String useOfVehicle;
    
    /**
     * 如果在非高峰车计划下
     */
    @Schema(title = "If under the off peak car scheme", description = "A flag indicating whether the vehicle is under an off-peak car scheme in an auto insurance policy.")
    private String ifUnderTheOffPeakCarScheme;
    
    /**
     * 运输货物类型
     */
    @Schema(title = "Types of goods carried", description = "The types of goods carried by the insured vehicle in an auto insurance policy.")
    private String typesOfGoodsCarried;
    
    /**
     * carOwner信息信息
     */
    @Schema(title = "Car owner", description = "The owner of the car associated with a policy.")
    private InsuredAutoCarOwnerResponse carOwner;

    /**
     * claimExperience信息信息
     */
    @Schema(title = "Claim experience", description = "Information about the claim experience related to the policy.")
    private InsuredAutoClaimExperienceResponse claimExperienceInfo;

    /**
     * 车架号 Vehicle Identification Number
     */
    @Schema(title = "Vehicle identification number", description = "The unique identification number assigned to a vehicle in an auto insurance policy.")
    private String vehicleIdentificationNumber;
    
    @Schema(title = "Vehicle other Info", description = "Other information associated with a policy or transaction.")
    private InsuredAutoOtherInfoResponse otherInfo;
    
    @Schema(title = "Vehicle loan", description = "The loan information associated with a policy or transaction.")
    private InsuredAutoLoanResponse loan;
    
    @Schema(title = "Insured object type", description = "Insured object type")
    private ObjectType objectType;
    
    /**
     * chassis No
     */
    @Schema(title = "chassisNo")
    private String chassisNo;
    
    /**
     * engine No
     */
    @Schema(title = "engineNo")
    private String engineNo;
    
    /**
     * number Of Seat
     */
    @Schema(title = "numberOfSeat")
    private int numberOfSeat;
    
    /**
     * plate No
     */
    @Schema(title = "plateNo")
    private String plateNo;
    
    /**
     * purchase Price
     */
    @Schema(title = "purchasePrice")
    private String purchasePrice;
    
    /**
     * registration Category
     */
    @Schema(title = "registrationCategory")
    private String registrationCategory;
    
    /**
     * registration Date
     */
    @Schema(title = "registrationDate")
    private Date registrationDate;
    
    /**
     * registration No
     */
    @Schema(title = "registrationNo")
    private String registrationNo;
    
    /**
     * vehicle Capacity
     */
    @Schema(title = "vehicleVolume")
    private String vehicleVolume;
    
    /**
     * Engine Type
     */
    @Schema(title = "engineType")
    private String engineType;
    
    /**
     * vehicle Make
     */
    @Schema(title = "vehicleBrand")
    private String vehicleBrand;
    
    /**
     * vehicle Model
     */
    @Schema(title = "vehicleModel")
    private String vehicleModel;
    
    /**
     * vehicle Type
     */
    @Schema(title = "vehicleType")
    private String vehicleType;
    
    /**
     * vehicle Usage
     */
    @Schema(title = "vehicleUsage")
    private String vehicleUsage;
    
    /**
     * vinNo
     */
    @Schema(title = "vinNo")
    private String vinNo;
    
    /**
     * Vehicle Group
     */
    @Schema(title = "vehicleGroup")
    private String vehicleGroup;
    
    /**
     * Vehicle Body Type
     */
    @Schema(title = "vehicleBodyType")
    private String vehicleBodyType;
    
    /**
     * Engine Power
     */
    @Schema(title = "enginePower")
    private String enginePower;
    
    /**
     * Registration Area
     */
    @Schema(title = "registrationArea")
    private String registrationArea;
    
    /**
     * Vehicle Color
     */
    @Schema(title = "vehicleColor")
    private String vehicleColor;
    
    /**
     * Vehicle Loan
     */
    @Schema(title = "vehicleLoan")
    private YesNoEnum vehicleLoan;
    
    /**
     * Vehicle Invoice Value
     */
    @Schema(title = "vehicleInvoiceValue")
    private String vehicleInvoiceValue;
    
    /**
     * Year manufacturing
     */
    @Schema(title = "yearOfManufacturing")
    private Date yearOfManufacturing;
    
    @Schema(title = "Plate registration zone")
    private String plateRegistrationZone;

    /**
     * vehicle Color
     */
    @Schema(title = "vehicleValue")
    private String vehicleValue;

    /**
     * invoice Color
     */
    @Schema(title = "invoiceValue")
    private String invoiceValue;
    
    @Schema(title = "VAT status for vehicle")
    private String vatStatusForVehicle;
    
    @Schema(title = "Loan Company")
    private String loanCompany;
    
    @Schema(title = "Loan Contract No.")
    private String loanContractNo;
    
    @Schema(title = "Date of Contract")
    private Date dateOfContract;
    
    @Schema(title = "Machine usage type")
    private String machineUsageType;
    
    @Schema(title = "Truck type")
    private String truckType;
    
    @Schema(title = "Motorbike type")
    private String motorbikeType;
    
    @Schema(title = "Trailer type")
    private String trailerType;
    
    @Schema(title = "Vehicle equipment package")
    private String vehicleEquipmentPackage;
    
    @Schema(title = "Load capacity")
    private String loadCapacity;
    
    @Schema(title = "Max allowed weight")
    private String maxAllowedWeight;
    
    @Schema(title = "Bus geographical usage")
    private String busGeographicalUsage;
    
    @Schema(title = "Usage & Class of bus")
    private String usageAndClassOfBus;
    
    @Schema(title = "Machine class")
    private String machineClass;
    
    @Schema(title = "Engine volume")
    private String engineVolume;
    
    @Schema(title = "Extra equipment value")
    private String extraEquipmentValue;
    
    @Schema(title = "Extra equipment name")
    private String extraEquipmentName;
    
    @Schema(title = "vehicle In Advertising And Branding")
    private String vehicleInAdvertisingAndBranding;
    
    @Setter
    @Getter
    @ToString
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class AdditionalEquipmentResponse extends ObjectCommonBase {
        
        /**
         * Equipment serial no
         */
        @JsonAlias("equipmentCode")
        @Schema(title = "Equipment Serial No", description = "The serial number assigned to equipment associated with an insurance policy.")
        private String equipmentSerialNo;
        
        /**
         * Equipment name
         */
        @Schema(title = "Equipment Name", description = "The name of the equipment being insured.")
        private String equipmentName;
        
        /**
         * Equipment price
         */
        @Schema(title = "Equipment Price", description = "The price or value of equipment associated with an insurance policy.")
        private String equipmentPrice;
        /**
         * Equipment description
         */
        @Schema(title = "Equipment Description", description = "A detailed description of equipment associated with an insurance policy.")
        private String equipmentDescription;

    }
    
    @Schema(title = "insured auto car owner response")
    @Getter
    @Setter
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class InsuredAutoCarOwnerResponse extends ObjectCommonBase {

        /**
         * 车主身份类型
         */
        @Schema(title = "Id type", description = "The type of identification document used by the insured individual in an insurance policy.")
        private CertiTypeEnum idType;
        
        /**
         * 车主姓名
         */
        @Schema(title = "Name", description = "The name of the individual or organization.")
        private String name;
        
        /**
         * 车主身份号码
         */
        @Schema(title = "Id number", description = "The unique number assigned to the identification document of the insured individual in an insurance policy.")
        private String idNumber;
        
        /**
         * 车主性别
         */
        @Schema(title = "Gender", description = "The socially constructed characteristics of women and men, such as norms, roles, and relationships of and between groups of women and men. It varies from society to society and can be changed.")
        private GenderEnum gender;
        
        /**
         * 车主生日
         */
        @Schema(title = "Birthday", description = "The date of birth, which is a specific day a person was born into the world. It includes the day, month, and year when an individual entered the world.")
        private LocalDate birthday;
        
        /**
         * 顾客类型
         */
        @Schema(title = "Customer type", description = "The categorization or classification of individuals or entities based on whether they are buying insurance for personal or business/institutional needs. Valid values: Individual or Organization.")
        private String customerType;
        
        /**
         * 拥有车辆数
         */
        @Schema(title = "Number of vehicle owned", description = "The number of vehicles owned by the insured individual in an auto insurance policy.")
        private String numberOfVehicleOwned;
        
        @Schema(title = "First name", description = "The first name of a customer. It is the given name by which a person is commonly known and identified in day-to-day interactions.")
        private String firstName;
        
        @Schema(title = "Middle name", description = "A name that is often placed between a person's first name and last name in their full name. It is optional.")
        private String middleName;
        
        @Schema(title = "Last name", description = "The surname or family name of a customer. It is the name shared by all members of a family and is usually passed down from generation to generation.")
        private String lastName;
        
        @Schema(title = "Combine name", description = "A combined name field for additional information in an insurance policy.")
        private String nameCombine;
        
        @Schema(title = "First name2", description = "An alternative first name for the individual.")
        private String firstName2;
        
        @Schema(title = "Middle name2", description = "An alternative middle name for the individual.")
        private String middleName2;
        
        @Schema(title = "Last name2", description = "An alternative last name or surname for the individual.")
        private String lastName2;
        
        @Schema(title = "Combine name2", description = "A combined name field for additional information in an insurance policy.")
        private String nameCombine2;
        
        @Schema(title = "First name3", description = "An additional first name for the individual.")
        private String firstName3;
        
        @Schema(title = "Middle name3", description = "An additional middle name for the individual.")
        private String middleName3;
        
        @Schema(title = "Last name3", description = "An additional last name or surname for the individual.")
        private String lastName3;
        
        @Schema(title = "Combine name3", description = "A combined name field for additional information in an insurance policy.")
        private String nameCombine3;
        
        @Schema(title = "Relationship With Policyholder", description = "The relationship of the insured to the policyholder (e.g., Self, Spouse, Child).")
        private RelationEnum relationshipWithPolicyholder;
        
    }
    
    @Schema(title = "insured auto car owner response")
    @Getter
    @Setter
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class InsuredAutoDriverResponse extends ObjectCommonBase {
        
        /**
         * 与车主关系
         */
        @Schema(title = "Relationship with the owner", description = "The relationship of the main insured to the policyholder, such as self, spouse, or dependent.")
        private RelationEnum mainInsuredRelation;
        
        /**
         * 姓名
         */
        @Schema(title = "Name", description = "The name of the individual or organization.")
        private String name;
        
        /**
         * 证件号
         */
        @Schema(title = "Certi no", description = "A unique identifier assigned to a specific certification or document of an individual or organization customer to make it easy to identify and verify. For example, ID number, FIN number, and passport number.")
        private String certiNo;
        
        /**
         * 证件类型
         */
        @Schema(title = "Certi type", description = "The type of certification or document associated with the individual or organization.")
        private CertiTypeEnum certiType;
        
        /**
         * 出生日期
         */
        @Schema(title = "Birthday", description = "The date of birth, which is a specific day a person was born into the world. It includes the day, month, and year when an individual entered the world.")
        private LocalDate birthday;
        
        /**
         * 性别
         */
        @Schema(title = "Gender", description = "The socially constructed characteristics of women and men, such as norms, roles, and relationships of and between groups of women and men. It varies from society to society and can be changed.")
        private GenderEnum gender;
        
        /**
         * 婚姻状态
         */
        @Schema(title = "Marital status", requiredMode = RequiredMode.REQUIRED, description = "The marital status of the insured individual in an insurance policy.")
        private MaritalStatusEnum maritalStatus;
        
        /**
         * 职业
         */
        @Schema(title = "Profession", requiredMode = RequiredMode.REQUIRED, description = "The profession of the insured individual in an insurance policy.")
        private String profession;
        
    }
    
    @Schema(title = "insured auto inspection response")
    @Getter
    @Setter
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class InsuredAutoInspectionResponse extends ObjectCommonBase {
        
        @Schema(title = "The platform code", description = "The platform or system used for a specific process or transaction.")
        private String platform;
        
        @Schema(title = "The document type", description = "The classification of the document, such as policy, claim, or underwriting document.")
        private String documentType;
        
        @Schema(title = "The file unique code", description = "The unique code assigned to a file or document related to an insurance claim or policy.")
        private String fileUniqueCode;
        
        @Schema(title = "The damage part of the auto", description = "The classification or type of damage sustained by an insured object, such as collision, fire, or theft.")
        private VehiclePartEnum part;
        
        @Schema(title = "The damage part of the auto", description = "The classification or type of damage sustained by an insured object, such as collision, fire, or theft.")
        private VehicleDamageTypeEnum damageType;
        
        @Schema(title = "The damage coordinate of the auto", description = "The geographical coordinates (latitude and longitude) associated with an event or location relevant to an insurance claim.")
        private String coordinate;
    }
    
    @Schema(name = "Insured Auto Loan Response", description = "Insured Auto Loan Response")
    @Getter
    @Setter
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class InsuredAutoLoanResponse extends ObjectCommonBase {
        
        @Schema(title = "Loan Company", description = "The name of the company or financial institution providing the loan for an insured asset (e.g., vehicle, property).")
        private String loanCompany;
        
        @Schema(title = "Loan Contract No.", description = "The unique identifier assigned to a loan contract associated with an insured asset.")
        private String loanContractNo;
        
        @Schema(title = "Date of Contract", description = "The date when the insurance contract or agreement was officially signed or executed.")
        private Date dateOfContract;

    }

    @Schema(title = "insured auto other info response")
    @Getter
    @Setter
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class InsuredAutoOtherInfoResponse extends ObjectCommonBase {
        
        @Schema(title = "Platform code", description = "The platform or system used for a specific process or transaction.")
        private String platform;
        
        @Schema(title = "Insured primary key ID", description = "The system-generated unique identifier for the insured person or entity.")
        private Long insuredId;
        
        @Schema(title = "Vehicle number", description = "The unique identifier for a vehicle.")
        private String vehicleNo;
        
        @Schema(title = "Vehicle need examination flag", description = "A flag indicating whether a vehicle examination is required.")
        private YesNoEnum needVehicleExamination;
        
        @Schema(title = "Vehicle examination way", description = "The method or process used to examine a vehicle.")
        private String vehicleExaminationWay;
        
        @Schema(title = "Examination result", description = "The result of an examination or inspection.")
        private String examinationResult;
        
        @Schema(title = "Examination result detail", description = "The detailed result of an examination or inspection.")
        private String examinationResultDetail;
        
        @Schema(title = "Provide vehicle photo later x days", description = "The number of days allowed to provide vehicle photos after an examination.")
        private Integer provideVehiclePhotoLaterDays;
        
        @Schema(title = "Vehicle examination areas", description = "The area or location where a vehicle examination takes place.")
        private String vehicleExaminationArea;
        
        @Schema(title = "Vehicle examination description", description = "The description of an examination or inspection.")
        private String examinationDescription;
        
        @Schema(title = "Examination detail", description = "The detailed information related to an examination or inspection.")
        private String examinationDetail;
        
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @NoArgsConstructor
    @Schema(title = "Policy insured auto claim experience response", description = "Policy insured auto claim experience response")
    public static class InsuredAutoClaimExperienceResponse extends ObjectCommonBase {

        /**
         * N年无理赔（保单年度）
         */
        @Schema(title = "No of accident free years", description = "The number of consecutive years the insured has been without an at-fault accident. Used for calculating No Claim Discount (NCD).")
        private String noOfAccidentFreeYears;

        /**
         * ncd金额
         */
        @Schema(title = "NCD amount", description = "The monetary value of the No Claim Discount (NCD) applied to the premium.")
        private BigDecimal ncdAmount;

        /**
         * NCD折扣或者加费比例
         */
        @Schema(title = "NCD percent", description = "The percentage of No Claim Discount (NCD) applicable to the vehicle, used to determine the insurance premium.")
        private String ncdPercent;

        /**
         * 今年预估事故等级
         */
        @Schema(title = "Accident degree", description = "The degree of severity of accidents associated with the vehicle, used to assess the risk and determine the insurance premium.")
        private String accidentDegree;

        /**
         * 历史理赔理赔金额
         */
        @Schema(title = "Total claim amount", description = "The total amount of claims made in an insurance policy.")
        private String totalClaimAmount;

        /**
         * 有无保单
         */
        @Schema(title = "Has original pol", description = "Indicates whether the vehicle has an original policy, used for tracking and verification purposes.")
        private String hasOriginalPol;

        /**
         * 去年事故数
         */
        @Schema(title = "Accident number last year", description = "The number of accidents the vehicle was involved in during the last year, used to assess the risk and determine the insurance premium.")
        private String accidentNumberLastYear;

        /**
         * 保单号
         */
        @Schema(title = "Original policy no", description = "The original policy number associated with the vehicle, used for tracking and verification purposes.")
        private String originalPolNo;

        /**
         * 过去三年事故数
         */
        @Schema(title = "Accident number in last 3 year", description = "The number of accidents the vehicle was involved in during the last three years, used to assess the risk and determine the insurance premium.")
        private String accidentNumberInLast3Year;

        /**
         * 过去三年理赔案件数
         */
        @Schema(title = "Claim number in last 3 year", description = "The number of claims associated with the vehicle in the last three years, used to assess the risk and determine the insurance premium.")
        private String claimNumberInLast3Year;

        /**
         * 去年理赔案件数
         */
        @Schema(title = "Claim number last year", description = "The number of claims associated with the vehicle in the last year, used to assess the risk and determine the insurance premium.")
        private String claimNumberLastYear;

        /**
         * 过去五年理赔案件数
         */
        @Schema(title = "Number of claims settled in the past five years", description = "The number of claims associated with the vehicle in the last five years, used to assess the risk and determine the insurance premium.")
        private String claimNumberInLast5Year;

        /**
         * 损失率
         */
        @Schema(title = "Loss ratio", description = "The loss ratio for the vehicle in the last year, used to assess the risk and determine the insurance premium.")
        @JsonAlias({"lossRatioLastYear", "lossRatio"})
        private String lossRatioLastYear;

        /**
         * 过去三年理赔案件数
         */
        @Schema(title = "Claim number in last three year", description = "The number of claims made in the last three years in an auto insurance policy.")
        private String claimNumberInLastThreeYear;

        /**
         * 过去五年理赔案件数
         */
        @Schema(title = "Claim number in last five year", description = "The number of claims made in the last five years in an insurance policy.")
        private String claimNumberInLastFiveYear;

        /**
         * 过去三年事故数
         */
        @Schema(title = "Accident number in last three year", description = "The number of accidents in the last three years in an auto insurance policy.")
        private String accidentNumberInLastThreeYear;

    }
}
