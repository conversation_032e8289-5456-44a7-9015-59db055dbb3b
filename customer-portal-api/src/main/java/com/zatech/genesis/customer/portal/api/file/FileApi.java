package com.zatech.genesis.customer.portal.api.file;

import com.zatech.genesis.customer.portal.api.file.response.FileResponse;

import io.swagger.v3.oas.annotations.Operation;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping("/api/file")
public interface FileApi {

    @Operation(summary = "upload file")
    @PostMapping(value = {"/upload"})
    public FileResponse uploadFile(@RequestParam(value = "file") MultipartFile multipartFile, @RequestParam(required = false) String contentType);

    @Operation(summary = "file preview")
    @GetMapping(value = {"/preview/{fileUniqueCode}"})
    public void preview(@PathVariable("fileUniqueCode") String fileUniqueCode,
                        @RequestParam(value = "width", required = false) Integer width,
                        @RequestParam(value = "height", required = false) Integer height);
}
