package com.zatech.genesis.customer.portal.api.errorcode;

import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

public enum PaymentCheckErrorCode implements IErrorCode {
    PAYMENT_PAYER_NOT_FOUND,

    PAYMENT_STATUS_ERROR,

    EFFECTIVE_DATE_VALIDATION_ERROR,

    PAYMENT_DATA_NOT_FOUND,

    PAYMENT_ORDER_STATUS_PAYED,

    PAYMENT_PREMIUM_CHANGED;

    @Override
    public String getModuleName() {

        return "payment";
    }

    @Override
    public String getErrorCode() {

        return name();
    }
}
