/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.biz.AttachmentRefTypeEnum;
import com.zatech.gaia.resource.components.enums.issuance.AttachmentSourceEnum;
import com.zatech.gaia.resource.components.enums.issuance.OcrStatusEnum;
import com.zatech.gaia.resource.graphene.customer.AttachmentTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-03-13 9:31
 */
@Schema(title = "Insured Attachment")
@Getter
@Setter
@Accessors(chain = true)
public class InsuredAttachment extends ObjectCommonBase {

    /**
     * ref_type
     */
    @Schema(title = "Ref Type", description = "The type of reference, such as policy, claim, or liability.")
    private AttachmentRefTypeEnum refType;

    /**
     * 附件url
     */
    @Schema(title = "Attachment Url", description = "The URL or link to access the attached document or file.")
    private String attachmentUrl;

    /**
     * 附件名称
     */
    @Schema(title = "Attachment Name", description = "The name or title of the attached document or file.")
    private String attachmentName;

    /**
     * 附件类型
     */
    @Schema(title = "Attachment Type", description = "The type of attachment, such as a PDF, image, or spreadsheet, used in the insurance process.")
    private AttachmentTypeEnum attachmentType;

    /**
     * Attachment Info
     */
    @Schema(title = "Attachment Info", description = "Detailed information about the attachment, including its type, size, and format.")
    private String attachmentInfo;

    /**
     * 标的附件类型
     */
    @Schema(title = "Document Type", description = "The classification of the document, such as policy, claim, or underwriting document.")
    private String documentType;

    /**
     * 附件来源类型
     */
    private AttachmentSourceEnum attachmentSource = AttachmentSourceEnum.NEW_BUSINESS;

    /**
     * Ocr Status
     */
    @Schema(title = "Ocr Status", description = "The status of the OCR process, indicating whether it is pending, completed, or failed.")
    private OcrStatusEnum ocrStatus;

    /**
     * Ocr Result
     */
    @Schema(title = "Ocr Result", description = "The result of Optical Character Recognition (OCR) processing, typically used to extract text from scanned documents or images.")
    private String ocrResult;

}
