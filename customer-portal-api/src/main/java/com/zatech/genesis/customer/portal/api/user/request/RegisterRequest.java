package com.zatech.genesis.customer.portal.api.user.request;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString
@RequiredArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", include = JsonTypeInfo.As.EXISTING_PROPERTY, visible = true)
@JsonSubTypes(value = {
    @JsonSubTypes.Type(value = EmailRegisterRequest.class, name = EmailRegisterRequest.TYPE),
    @JsonSubTypes.Type(value = PhoneRegisterRequest.class, name = PhoneRegisterRequest.TYPE),
})
public abstract class RegisterRequest {

    private final String type;

    private String channelCode;

    private String verificationCode;

    private String nonce;

}
