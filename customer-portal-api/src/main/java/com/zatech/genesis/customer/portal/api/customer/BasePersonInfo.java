/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.customer;

import com.zatech.gaia.resource.graphene.common.CountryNationalityEnum;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BasePersonInfo extends DynamicMetaField {

    private String contactPersonName;

    List<EmailInfo> emails;

    List<Address> addresses;

    List<Phone> phones;

    List<ContactPerson> contactPersons;

    List<Account> accounts;

    private CountryNationalityEnum nationality;

    private Boolean issueWithoutPayment;

    public boolean addressesCompare(BasePersonInfo target) {
        if (target == null) {
            return false;
        }

        List<Address> thisSortedAddresses = Optional.ofNullable(addresses).orElse(Collections.emptyList()).stream()
            .sorted(Comparator.comparing(Address::getDiffId, Comparator.nullsFirst(String::compareTo)))
            .toList();

        List<Address> targetSortedAddresses = Optional.ofNullable(target.getAddresses()).orElse(Collections.emptyList()).stream()
            .sorted(Comparator.comparing(Address::getDiffId, Comparator.nullsFirst(String::compareTo)))
            .toList();

        return Objects.equals(StaticJsonParser.toJsonString(thisSortedAddresses), StaticJsonParser.toJsonString(targetSortedAddresses));

    }


}
