package com.zatech.genesis.customer.portal.api.user.response;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.genesis.customer.portal.api.user.base.Beneficiary;
import com.zatech.genesis.customer.portal.api.user.base.Channel;
import com.zatech.genesis.customer.portal.api.user.base.File;
import com.zatech.genesis.customer.portal.api.user.base.Insured;
import com.zatech.genesis.customer.portal.api.user.base.InsuredObjectResponse;
import com.zatech.genesis.customer.portal.api.user.base.PayerInfo;
import com.zatech.genesis.customer.portal.api.user.base.PolicyHolder;
import com.zatech.genesis.customer.portal.api.user.base.ProductSummary;
import com.zatech.genesis.customer.portal.api.user.base.PurchaseSummary;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/10/23
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
public class IssuanceDetailResponse extends PolicyBaseResponse {

    @Schema(title = "goodsId")
    private Long goodsId;

    @Schema(title = "goodsName")
    private String goodsName;

    @Schema(title = "policyNo")
    private String policyNo;

    @Schema(title = "proposalNo")
    private String proposalNo;

    @Schema(title = "status")
    private IssuanceStatusEnum status;

    @Schema(title = "premium")
    private String premium;

    @Schema(title = "commission")
    private String commission;

    @Schema(title = "commissionCurrency")
    private CurrencyEnum commissionCurrency;

    @Schema(title = "paymentStatus")
    private String paymentStatus;

    @Schema(title = "policyHolder")
    private PolicyHolder policyHolder;

    @Schema(title = "hasAttachment")
    private YesNoEnum hasAttachment;

    @Schema(title = "insureds")
    private List<Insured> insureds;

    @Schema(title = "beneficiaries")
    private List<Beneficiary> beneficiaries;

    @Schema(title = "insuredObjectList")
    private List<InsuredObjectResponse> insuredObjectList;

    @Schema(title = "proposalInsuredObjectResponse")
    private List<File> attachmentList;

    @Schema(title = "purchaseSummary")
    private PurchaseSummary purchaseSummary;

    @Schema(title = "productSummary")
    private ProductSummary productSummary;

    @Schema(title = "支付人信息")
    private List<PayerInfo> policyPayerList;

    @Schema(title = "Relation NO.")
    private List<String> relationNos;

    @Schema(title = "Schema")
    private String schema;

    @Schema(title = "effectiveDate")
    private Date effectiveDate;

    @Schema(title = "expireDate")
    private Date expireDate;

    @Schema(title = "保障期间")
    private String coveragePeriod;

    @Schema(title = "channel")
    private Channel channel;

    @Schema(title = "temporary")
    private Boolean temporary;
}
