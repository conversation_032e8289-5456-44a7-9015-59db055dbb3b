/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.customer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.genesis.customer.portal.api.i18n.I18n;
import com.zatech.genesis.customer.portal.api.i18n.I18nKey;

import java.time.LocalDate;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Individual extends BasePersonInfo {

    private Boolean disabilityOrNot;

    @I18n(key = I18nKey.GENDER)
    private GenderEnum gender;

    private String occupationCode;

    private String middleName;

    private String fullName;

    private String lastName;

    private String firstName;

    @JsonProperty("idValue")
    private String certiNo;

    @I18n(key = I18nKey.CERTIFICATION_TYPE)
    @JsonProperty("idType")
    private CertiTypeEnum certiType;

    private LocalDate birthday;

}

