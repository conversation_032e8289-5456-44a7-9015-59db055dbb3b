/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.customer;

import com.zatech.gaia.resource.components.enums.common.AddressTypeEnum;
import com.zatech.genesis.customer.portal.api.i18n.I18n;
import com.zatech.genesis.customer.portal.api.i18n.I18nKey;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Address extends DynamicMetaField {

    private String address25;

    private String address24;

    private String address23;

    private String address22;

    private String address21;

    @I18n(key = I18nKey.ADDRESS_16)
    private String address16;

    private String address15;

    private String address14;

    private String address13;

    private String address12;

    private String address11;

    private String address26;

    private String zipCode;

    private AddressTypeEnum addressType;

    private String diffId;

    private Date gmtCreated;

    private Date gmtModified;

}
