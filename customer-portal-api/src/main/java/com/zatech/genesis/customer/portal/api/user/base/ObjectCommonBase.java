/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
package com.zatech.genesis.customer.portal.api.user.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-03-13 10:36
 */
@Schema(title = "object common base", description = "object common base")
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class ObjectCommonBase extends DynamicMetaField {

    @Schema(title = "Remark", description = "Additional remarks or notes associated with the individual or organization.")
    private String remark;

    @Schema(title = "Extra info", description = "Additional information or metadata associated with the individual or organization.")
    private String extraInfo;

    @Schema(title = "ExtraInfoField", description = "A field for storing any additional information relevant to the address that doesn't fit into the standard address fields. This can be used for specific project or tenant requirements.")
    private Map<?,?> extraInfoField;

}
