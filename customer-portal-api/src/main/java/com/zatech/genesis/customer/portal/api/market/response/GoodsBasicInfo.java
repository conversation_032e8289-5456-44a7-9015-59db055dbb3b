/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.market.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025/3/14 10:25
 */
@Data
public class GoodsBasicInfo {

    private String code;

    private Long goodsId;

    private String isMasterPolicy;

    private String goodsVersion;

    private String categoryName;

    private Long orgId;

    private String goodsStatus;

    private String orgCode;

    private String usageBased;

    private String goodsCode;

    private String goodsName;

    private Long categoryId;

    private String goodsDesc;
}
