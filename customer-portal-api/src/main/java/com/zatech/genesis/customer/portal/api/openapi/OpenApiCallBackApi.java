/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.api.openapi;

import com.zatech.genesis.customer.portal.api.openapi.request.CallbackRequest;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

@Tag(name = "OpenApiCallBack API")
@RequestMapping("/api/callback")
public interface OpenApiCallBackApi {

    @PostMapping
    void callback(@RequestHeader("x-openapi-tenant") String tenantCode,
                       @RequestHeader("x-openapi-channel") String channelCode,
                       @RequestBody CallbackRequest callbackRequest);

}
