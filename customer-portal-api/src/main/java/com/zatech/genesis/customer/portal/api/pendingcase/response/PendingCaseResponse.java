package com.zatech.genesis.customer.portal.api.pendingcase.response;

import com.zatech.genesis.customer.portal.api.pendingcase.base.DocumentInfo;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PendingCaseResponse {

    private BusinessInfoResponse businessInfo;

    private List<DocumentFile> documentFileList;

    @Getter
    @Setter
    public static class DocumentFile {

        private String docType;

        List<DocumentInfo> documentInfoList;
    }

}
