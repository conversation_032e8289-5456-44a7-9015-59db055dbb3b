package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.bcp.AccountTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.AccountPurposeEnum;
import com.zatech.gaia.resource.components.enums.customer.AddAndDelActionEnum;
import com.zatech.gaia.resource.components.permission.enums.common.YesNoEnum;
import com.zatech.gaia.resource.graphene.customer.AccountSubTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
public class Account {
    /**
     * 用户ID
     */
    @Schema(title = "Account ID", description = "The system-generated unique code used to identify a person's account and retrieve the details of account information.")
    private Long accountId;

    /**
     * 银行code
     */
    @Schema(title = "Bank code", description = "The unique identification number assigned to financial institutions, such as banks, by regulatory authorities. It is configured in the Account section of customer details in Customer Center.")
    private String bankCode;

    /**
     * 分行code
     */
    @Schema(title = "Bank branch code", description = "The unique identifier assigned to a specific branch of a bank. It helps in distinguishing one branch from another within the same bank. It is configured in the Account section of customer details in Customer Center.")
    private String bankBranchCode;

    /**
     * 分行code
     */
    @Schema(title = "Bank branch name", description = "The specific designation or title of a branch of a bank. It distinguishes one branch from another within the same bank network. It is configured in the Account section of customer details in Customer Center.")
    private String bankBranchName;

    @Schema(title = "Account type", description = "The type of account, such as savings, checking, or credit.")
    private AccountTypeEnum accountType;

    @Schema(title = "Account sub type", description = "The type of sub account, which is the middle level of the cascade account hierarchy defined in \"Payment/Collection Method & Account\" within Configuration Center > Tenant Data Configuration. The lowest level corresponds to the account field level in this hierarchy.")
    private AccountSubTypeEnum accountSubType;

    /**
     * 持卡人
     */
    @Schema(title = "CardHolder name", description = "The name of a cardholder who has been issued a payment card, such as a credit card, debit card, or prepaid card, by a financial institution or card issuer.")
    private String cardHolderName;

    /**
     * 银行卡号
     */
    @Schema(title = "Card number", description = "The unique numerical sequence assigned to a payment card, such as a credit card, debit card, or prepaid card. This number serves as the primary identifier for the card and is used in financial transactions to link the card to the cardholder's account.")
    private String cardNumber;

    /**
     */
    @Schema(title = "Third party pay voucher", description = "A voucher or proof of payment from a third-party payment system.")
    private String thirdPartyPayVoucher;

    /**
     * 有效期
     */
    @Schema(title = "Expiry date", description = "The date on which a proposal or policy expires.")
    private LocalDate expiryDate;

    /**
     * 银行卡预留手机号
     */
    @Schema(title = "Mobile No.", description = "The unique numerical sequence assigned to a mobile device to make and receive phone calls, text messages, and other forms of communication.")
    private String mobileNo;

    /**
     */
    @Schema(title = "Safe No.", description = "The unique identifier for a safe or secure storage unit.")
    private String safeNo;

    /**
     * 银行名称
     */
    @Schema(title = "Bank name", description = "The name of the customer's bank. It is configured in the Account section of customer details in Customer Center.")
    private String bankName;

    /**
     * Bank City
     */
    @Schema(title = "Bank city", description = "The city where the bank that issued the card is located.")
    private String bankCity;

    /**
     * 状态：1-有效，2-无效
     */
    @Schema(title = "Status", description = "The status of the record")
    private YesNoEnum status;

    @Schema(title = "Is default", description = "A flag indicating whether the value or setting is the default.")
    private YesNoEnum isDefault;

    /**
     * 是否删除
     */
    @Schema(title = "Is deleted", description = "Indicates whether the entity or record is deleted.")
    private YesNoEnum isDeleted;

    @Schema(title = "Extension map", description = "A map of extensions associated with the policy.")
    private Map<String, String> extensionMap;

    @Deprecated(since = "2.60", forRemoval = true)
    /**
     * @deprecated 账户的用途
     */
    @Schema(title = "Account purpose", description = "The purpose of the account (e.g., personal, business).", allowableValues = {"GENERAL",
            "CASH_BONUS", "SURVIVAL_BENEFIT", "MATURITY_BENEFIT", "ANNUITY"})
    List<AccountPurposeBaseResponse> accountPurpose;

    /**
     * The International Bank Account Number
     */
    @Schema(title = "International bank account number", description = "The International Bank Account Number (IBAN) for a financial account.")
    private String iban;

    /**
     * SWIFT code
     */
    @Schema(title = "Swift code", description = "The SWIFT code, officially known as the Business Identifier Code (BIC), is an alphanumeric code used by banks globally to identify financial institutions during international transactions uniquely. This code is managed by the Society for Worldwide Interbank Financial Telecommunications.")
    private String swiftCode;

    /**
     * 银行地址
     */
    @Schema(title = "Bank address", description = "The physical location where a bank is situated, encompassing details such as the street address, city, state or province, postal code, and country.")
    private String bankAddress;

    /**
     * 银行支行地址
     */
    @Schema(title = "Bank branch address", description = "The physical location where a bank branch is situated, encompassing details such as the street address, city, state or province, postal code, and country.")
    private String bankBranchAddress;

    @Data
    public static class AccountPurposeBaseResponse {

        @Deprecated(since = "2.60", forRemoval = true)
        @Schema(title = "Account purpose", description = "The purpose of the account (e.g., personal, business).")
        private AccountPurposeEnum accountPurpose;

        @Schema(title = "Action", description = "The action being performed (e.g., Create, Update, Delete).")
        private AddAndDelActionEnum action;
    }

}
