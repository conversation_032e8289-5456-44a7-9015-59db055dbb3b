package com.zatech.genesis.customer.portal.api.pos.response;

import com.zatech.gaia.resource.components.enums.posonline.PosStatusEnum;
import com.zatech.genesis.customer.portal.api.customer.Document;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PosDetailResponse {

    private String policyNo;

    private String caseNo;

    private List<PosTransactionResponse> transactions;

    private PosStatusEnum caseStatus;

    private List<Document> documents;

    private PosPolicyHolderResponse holder;

    private List<PosPolicyInsurantResponse> insurants;
    
    private PosPolicyHolderResponse holderBefore;
    
    private List<PosPolicyInsurantResponse> insurantsBefore;

    private Boolean needSyncInsured;

}
