package com.zatech.genesis.customer.portal.api.user.base;

import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.gaia.resource.components.enums.schema.CustomerTypeEnum;
import com.zatech.gaia.resource.components.enums.schema.OrganizationIDTypeEnum;
import com.zatech.genesis.customer.portal.api.i18n.I18n;
import com.zatech.genesis.customer.portal.api.i18n.I18nKey;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Schema(title = "BaseCustomerComponentDTO", description = "Customer properties")
@Setter
@Getter
@Accessors(chain = true)
public class BaseCustomerComponentDTO {

    private PartyTypeEnum customerType;

    @Schema(title = "middleName")
    private String middleName;

    @Schema(title = "gender")
    @I18n(key = I18nKey.GENDER)
    private GenderEnum gender;

    @Schema(title = "nickName")
    private String nickName;

    @Schema(title = "countryCode")
    private String countryCode;

    @Schema(title = "profilePhoto")
    private String profilePhoto;

    @Schema(title = "fullName")
    private String fullName;

    @Schema(title = "firstName")
    private String firstName;

    @Schema(title = "lastName")
    private String lastName;

    @Schema(title = "Formatted last name")
    private String formattedLastName;

    @Schema(title = "Formatted middle name")
    private String formattedMiddleName;

    @Schema(title = "Formatted first name")
    private String formattedFirstName;

    @Schema(title = "Formatted full name")
    private String formattedFullName;

    @Schema(title = "Full name2")
    private String fullName2;

    @Schema(title = "Last name2")
    private String lastName2;

    @Schema(title = "Middle name2")
    private String middleName2;

    @Schema(title = "First name2")
    private String firstName2;

    @Schema(title = "Formatted last name2")
    private String formattedLastName2;

    @Schema(title = "Formatted middle name2")
    private String formattedMiddleName2;

    @Schema(title = "Formatted first name2")
    private String formattedFirstName2;

    @Schema(title = "Formatted full name2")
    private String formattedFullName2;

    @Schema(title = "Full name3")
    private String fullName3;

    @Schema(title = "Last name3")
    private String lastName3;

    @Schema(title = "Middle name3")
    private String middleName3;

    @Schema(title = "First name3")
    private String firstName3;

    @Schema(title = "Labels")
    private List<Label> labels;

    @Schema(title = "Channel user No.")
    private String channelUserNo;

    @Schema(title = "Channel code")
    private String channelCode;

    @I18n(key = I18nKey.CERTIFICATION_TYPE)
    @Schema(title = "id type")
    private CertiTypeEnum certiType;

    @Schema(title = "certi No")
    private String certiNo;

    @Schema(title = "birthday")
    private LocalDate birthday;

    @Schema(title = "email")
    private String email;

    @Schema(title = "Email id")
    private Long emailId;

    @Schema(title = "Phone list")
    private List<Phone> phoneList;

    @Schema(title = "Address list")
    private List<Address> addressList;

    private List<Account> accounts;

    @Schema(title = "Phone")
    private String phone;

    @Schema(title = "Address")
    private String address;

    @Schema(title = "numberOfEmployees")
    private BigDecimal numberOfEmployees;

    @Schema(title = "isLeasing")
    private Boolean isLeasing;

    private Organization organization;

    @Schema(title = "extensions")
    Map<String, String> extensions;
}
