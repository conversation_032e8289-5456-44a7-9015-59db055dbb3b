<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zatech.genesis</groupId>
		<artifactId>customer-portal</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>customer-portal-api</artifactId>
	<name>customer-portal-api</name>
	<description>Customer Portal Api Module</description>
	<packaging>jar</packaging>
	<properties>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>customer-portal-share</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zhongan</groupId>
			<artifactId>zatech-octopus-common</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-core</artifactId>
		</dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>
		<dependency>
			<groupId>com.zhongan</groupId>
			<artifactId>zatech-resourcecode-enum-gaia</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zhongan.graphene</groupId>
			<artifactId>zatech-cdc-api</artifactId>
			<version>${revision}</version>
			<exclusions>
				<exclusion>
					<artifactId>springfox-swagger2</artifactId>
					<groupId>io.springfox</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis.portal</groupId>
			<artifactId>toolbox-test</artifactId>
			<version>${portal-toolbox.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zhongan.graphene</groupId>
			<artifactId>zatech-enum-product-market</artifactId>
			<version>${revision}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.zhongan.graphene</groupId>
			<artifactId>zatech-payment-gateway-api</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.zhongan.graphene</groupId>
					<artifactId>zatech-finance-toolbox</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.zhongan.graphene</groupId>
					<artifactId>zatech-script</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>customer-portal-common</artifactId>
			<version>${revision}</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>false</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>flatten</id>
						<phase>process-resources</phase>
						<goals>
							<goal>flatten</goal>
						</goals>
						<configuration>
							<pomElements>
								<distributionManagement>remove</distributionManagement>
								<repositories>remove</repositories>
							</pomElements>
						</configuration>
					</execution>
				</executions>
			</plugin>

		</plugins>
	</build>

</project>
